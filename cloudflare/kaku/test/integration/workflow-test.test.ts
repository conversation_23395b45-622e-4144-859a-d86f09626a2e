import { env, waitOnExecutionContext, createExecutionContext } from 'cloudflare:test';
import { describe, expect, it, vi } from 'vitest';
import app, { Connections } from '../../src/api';
import { ConnectionsWorkflowParams } from '../../src/workflow/types';
import {
  ConnectionWorkflowState,
  storeConnectionScreenshotToR2,
} from '../../src/common/utils/storeConnectionScreenshotToR2';
import { CreateLinkResponse } from '../../src/shared/coordinator-types';

describe('should render the initial htmx correctly', () => {
  it('should render service page with provided linkId and serviceId', async () => {
    const userId = 'testUser';
    const serviceId = 'facebook';

    const createLinkRequest = new Request('http://localhost:8787/create-link', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ userId: userId, serviceId: serviceId }),
    });
    const ctx = createExecutionContext();
    const createLinkResponse = await app.fetch(createLinkRequest, env, ctx);
    await waitOnExecutionContext(ctx)
    expect(createLinkResponse.status).toBe(200);
    const linkResponse = (await createLinkResponse.json()) as CreateLinkResponse;
    expect(linkResponse).toHaveProperty('linkId');

    // mock stub.eagerlyInitializeResources() to do nothing
    // else it will try to open up a browser and it fails.
    vi.spyOn(Connections.prototype, 'eagerlyInitializeResources').mockImplementation(async () => {
      return;
    });

    const request = new Request(`http://localhost:8787/${linkResponse.linkId}/${serviceId}?userId=${userId}`, { method: 'GET' });
    // @ts-ignore no execution Context on the request context
    const response = await app.fetch(request, env, { waitUntil: vi.fn() as any });

    expect(response.status).toBe(200);
    const text = await response.text();

    // Check that the response contains the expected rendered HTML content
    expect(text).toContain('Facebook');
    expect(text).toContain(userId);
  });

  it('it should store screenshots to R2', async () => {
    //Arrange
    const userId = 'u_testuser1234';
    const serviceId = 'facebook';
    const sessionId = '1234';

    const connectionsWorkflowParams: ConnectionsWorkflowParams = {
      platformId: serviceId,
      userId: userId,
      sessionId: sessionId,
    };

    const screenshotInput = 'this is our fake screenshot';

    const workflowState = ConnectionWorkflowState.Authenticated;

    //Act
    await storeConnectionScreenshotToR2(
      env.SCREENSHOTS_INBOUND_BUCKET,
      userId,
      serviceId,
      sessionId,
      workflowState,
      screenshotInput,
    );

    //Assert
    const expectedPath = `base64_screenshots/${userId}/${serviceId}/${sessionId}_${workflowState}_`;

    const response = await env.SCREENSHOTS_INBOUND_BUCKET.list({
      startAfter: expectedPath,
    });

    const completeKey = response.objects[0].key;

    const screenshotOutput = await env.SCREENSHOTS_INBOUND_BUCKET.get(completeKey);

    if (screenshotOutput) {
      const blob = await screenshotOutput.blob();
      const arrayBuffer = await blob.arrayBuffer();
      const uint8 = new Uint8Array(arrayBuffer);

      expect(uint8).toStrictEqual(convertBase64ToBytes(screenshotInput));
    } else {
      //Fail the test, response shouldn't be null
      expect(true).toBe(false);
    }
  });
});

function convertBase64ToBytes(input: string): ArrayBuffer {
  const binaryString = atob(input);
  const len = binaryString.length;
  const bytes = new Uint8Array(len);
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes as any;
}
