import { Hono } from 'hono';
import { agentsMiddleware } from 'hono-agents';
import { <PERSON>ku<PERSON>pp } from '../common/types';
import { DashboardLayoutCard, LayoutWithCard } from '../ui/components/layout';
import { passwordEncoder } from '../common/security/passwordEncoder';
import { capitalize } from '../workflow/utils/helpers';
import { BrowserStateService } from '../workflow/BrowserStateService';
import { R2BrowserStateRepository } from '../workflow/R2BrowserStateRepository';
import { SimulatedLoginHandler } from './simulated-login-handler';
import { CDPBrowserDataAdapter } from '../workflow/adapters/CDPBrowserDataAdapter';
import { BrowserServiceFactory } from '../workflow/services';
import { CDP } from '../browser/simple-cdp';
import { platformDetails, PlatformTypes } from '../ui/constants';
import { userAuthMiddleware } from './user-auth-middleware';
import { linkValidationMiddleware } from './middleware/link-validation';
import { getErrorMiddleware, handleRpcCall, ValidationError, NotFoundError } from '../common/error';

const app = new Hono<KakuApp>();

// Add global error handling middleware
app.use('*', (c, next) => {
  const environment = c.env.ENVIRONMENT || 'production';
  return getErrorMiddleware(environment)(c, next);
});

// Note: Agent class names are transformed to kebab-case in URLs
// Example: ConnectionAgent → /agents/connection-agent/[platformId]
// In our case, it's /agents/connections/[platformId]
app
  .use(
    '*',
    agentsMiddleware({
      options: {
        cors: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type', // 'Access-Control-Allow-Headers': 'Content-Type', //
        },
        prefix: 'agents',
        // onBeforeRequest: async (request) => {
        //   const response = await validateAuth(request);
        //   if (response instanceof Response) {
        //     return response;
        //   }
        // },
      },
      onError: (error) => {
        console.error('Agent middleware error:', error);
      },
    }),
  )
  .get('/:linkId/:serviceId', linkValidationMiddleware, async (c) => {
    const userId = c.get('userId');
    const linkId = c.get('linkId');
    const serviceId = c.get('serviceId');

    const connectionDOName = `${linkId}`;
    const agent = c.env.Connections.idFromName(connectionDOName);
    const stub = c.env.Connections.get(agent);
    await stub.setName(connectionDOName);
    c.executionCtx.waitUntil(
      stub.eagerlyInitializeResources(serviceId as PlatformTypes, userId, linkId),
    );

    return c.html(DashboardLayoutCard(userId, linkId, serviceId, capitalize(serviceId)));
  })
  .get('/:linkId/:serviceId/flow', linkValidationMiddleware, (c) => {
    const userId = c.get('userId');
    const serviceId = c.get('serviceId');
    const linkId = c.get('linkId');

    const url = `${c.env.KAKU_WS_ENDPOINT}/agents/connections/${linkId}`;
    return c.html(
      LayoutWithCard(
        { wsEndpoint: url, linkId },
        {
          serviceId: serviceId,
          serviceName: capitalize(serviceId),
          serviceLogo: `/fb.png`,
          serviceTitle: 'Testing Forms',
          formTitle: 'Testing Forms',
          serviceDescription: 'Testing Forms',
          liveViewToggleText: 'Show Live View',
          loadingText: 'Processing...',
        },
      ),
    );
  })
  .post('/create-link', async (c) => {
    const body = await c.req.json();
    const serviceId = body.serviceId as PlatformTypes;
    const userId = body.userId as string; // TODO: this will be removed once we have proper authentication

    // Validate input using new error system
    if (!serviceId) {
      throw ValidationError.missingField('serviceId');
    }
    if (!userId) {
      throw ValidationError.missingField('userId');
    }

    const platformDetails2 = platformDetails[serviceId];
    if (!platformDetails2) {
      throw ValidationError.invalidValue('serviceId', serviceId, Object.keys(platformDetails));
    }

    // Use RPC error handling for Durable Object calls
    const coordinator = c.env.CoordinatorDO.idFromName(userId);
    const stub = c.env.CoordinatorDO.get(coordinator);

    const linkResponse = await handleRpcCall(
      'CoordinatorDO',
      'createLink',
      () => stub.createLink(serviceId, userId),
      { userId, platform: serviceId },
    );

    return c.json(linkResponse);
  })

  .get('/test-password', async (c) => {
    const matches = await passwordEncoder().matches(
      'this is an awesome password',
      '{argon2@SpringSecurity_v5_8}$argon2id$v=19$m=16384,t=2,p=1$J1lDBqWTpije3hfNicl6nA$Ktm+S5MLIWMeGMJ3v7QWCCW6U83Ub4MUisYPwlG8Zcs',
    );
    return c.html(`Password matches: ${matches}`);
  })
  // DEPRECATED: Legacy endpoint using old userId:platformId naming scheme
  // New agents use linkId-based naming. This endpoint is kept for backward compatibility only.
  .get('/handle/:userId/:platformId', async (c) => {
    const userId = c.req.param().userId;
    const platformId = c.req.param().platformId;

    if (!userId || !platformId) {
      throw ValidationError.missingField(!userId ? 'userId' : 'platformId');
    }

    const connectionDOName = `${userId}:${platformId}`;
    const agent = c.env.Connections.idFromName(connectionDOName);
    const stub = c.env.Connections.get(agent);

    await handleRpcCall('Connections', 'setName', () => stub.setName(connectionDOName), {
      userId,
      platform: platformId,
    });

    await handleRpcCall(
      'Connections',
      'handleFlowInitiate',
      () => stub.handleFlowInitiate({ platform: 'test' }),
      { userId, platform: platformId },
    );

    return c.json({ success: true });
  })
  .post('/demo-session/:userId/:platformId', async (c) => {
    try {
      const userId = c.req.param().userId;
      const platformId = c.req.param().platformId;
      const browserStateService = new BrowserStateService(
        new R2BrowserStateRepository(c.env.SCREENSHOTS_INBOUND_BUCKET),
      );

      const storedSession = await browserStateService.getBrowserState(userId, platformId);

      if (!storedSession) {
        return c.json({
          success: false,
          error: 'No stored session found for this user and platform',
        });
      }

      console.log(`✓ Found stored session with ${storedSession.cookies.length} cookies`);

      const browserService = BrowserServiceFactory.createFromEnvironment(c.env);
      const browserSession = await browserService.createSession({
        browserArgs: [],
      });

      console.log('✓ Created new browser session');

      const cdpClient = new CDP({ webSocketDebuggerUrl: browserSession.wsEndpoint });
      const targetInfo = await cdpClient.Target.createTarget({ url: 'about:blank' });
      console.log('✓ Created new target:', targetInfo.targetId);

      const { sessionId } = await cdpClient.Target.attachToTarget({
        targetId: targetInfo.targetId,
        flatten: true,
      });

      await cdpClient.Page.enable(undefined, sessionId);
      await cdpClient.Runtime.enable(undefined, sessionId);

      console.log('✓ CDP session established with sessionId:', sessionId);

      const browserDataAdapter = new CDPBrowserDataAdapter(cdpClient, sessionId);
      await browserStateService.loadBrowserStateToPage(browserDataAdapter, userId, platformId);

      const targetUrl =
        platformDetails[platformId as keyof typeof platformDetails]?.loginLink ||
        'https://example.com';

      await cdpClient.Page.navigate({ url: targetUrl }, sessionId);

      await new Promise((resolve) => {
        const handler = ({ params }: { params: any }) => {
          if (params.name === 'networkIdle') {
            cdpClient.Page.removeEventListener('loadEventFired', handler);
            resolve(undefined);
          }
        };
        cdpClient.Page.addEventListener('loadEventFired', handler);

        setTimeout(resolve, 10000);
      });

      console.log(`✓ Navigated to ${targetUrl} with restored session`);

      await browserService.closeSession(browserSession.sessionId!);
      return c.json({
        success: true,
        message: 'Session demonstration completed successfully',
        details: {
          cookiesLoaded: storedSession.cookies.length,
          localStorageItems: Object.keys(storedSession.localStorageData).length,
          sessionStorageItems: Object.keys(storedSession.sessionStorageData).length,
          targetUrl: targetUrl,
        },
      });
    } catch (error) {
      console.error('Session demonstration failed:', error);
      return c.json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      });
    }
  });

app.route('/v1/simulated', SimulatedLoginHandler);

export default app;

export { ConnectionsWorkflow } from '../workflow/connections-workflow';
export { Connections } from '../agent/connection-agent';
export { CoordinatorDO } from '../coordinator/coordinator-do';
