
import { Context, Next } from 'hono';
import { <PERSON>kuApp } from '../../common/types';

export async function linkValidationMiddleware(c: Context<KakuApp>, next: Next) {
  const userId = c.req.query('userId') || 'u_hardCodedUserId'; // TODO: this will be removed once we have proper authentication
  const linkId = c.req.param().linkId;
  const serviceId = c.req.param().serviceId;

  if (!linkId || !serviceId) {
    return c.html(`<h1>Missing linkId or serviceId</h1>`, 400);
  }

  const coordinator = c.env.CoordinatorDO.idFromName(userId); // TODO: pedro mentioned this ID can be different
  const coordinatorStub = c.env.CoordinatorDO.get(coordinator);
  const linkInfo = await coordinatorStub.getStatus(linkId);

  if (linkInfo?.status !== 'active') {
    // TODO: Handle each status, create UI, I'll do in Figma and send it to Sachin to implement
    return c.html(`<h1>Link is not active</h1>`, 403);
  }

  c.set('userId', userId);
  c.set('linkId', linkId);
  c.set('serviceId', serviceId);
  c.set('coordinatorStub', coordinatorStub);

  await next();
} 