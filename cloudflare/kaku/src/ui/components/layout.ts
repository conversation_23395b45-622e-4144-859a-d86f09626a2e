import { html } from 'hono/html';
import { renderHelpBottomSheet } from './bottom-sheet';
import { ServiceContext } from './types';
import { HTMLReturnType } from '../types';
import { platformDetails } from '../constants';
import { KazeelPlatformLogo } from './kazeel-platform-logo';

export const LayoutWithCard = (
  { wsEndpoint, linkId }: { wsEndpoint: string; linkId: string },
  props: ServiceContext,
) => {
  return html` <!DOCTYPE html>
  <html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <title>Connect to ${props.serviceName} - Kazeel</title>
    <script src="https://unpkg.com/htmx.org@2.0.4"></script>
    <script src="https://unpkg.com/htmx.org/dist/ext/ws.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@plainsheet/core@latest/dist/plainsheet-core.umd.js"></script>
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/video-layout.css" />
    <link rel="stylesheet" href="/css/bottom-sheet.css" />
  </head>
  <body
    class="light bg-neutral-60 dark:bg-primary-50 h-screen flex items-center justify-center"
  >
  <div class="relative md:w-[435px]  md:rounded-xl shadow-md shadow-neutral-50 bg-surface">
    <div
      id="connection-flow-wrapper"
      class="h-screen flex flex-col justify-center items-center relative md:w-[435px] md:h-auto md:rounded-xl shadow-md shadow-neutral-50 bg-surface"
    >
      <div
        id="connection-flow"
        class="w-screen flex flex-col items-center justify-center bg-surface text-surface-on-surface md:h-[auto] md:min-h-[600px] md:w-[435px] md:rounded-xl"
        ws-connect="${wsEndpoint}/agents/connections/${linkId}"
        hx-ext="ws"
      ></div>

      <!-- Having Trouble Button (not on first step) -->
      <div id="help-trigger-container" class="w-full flex justify-center mt-4">
        <button
          id="help-trigger"
          class="text-[#7A757F] underline text-sm font-medium focus:outline-none"
          style="display: none;"
        >
          Having Trouble?
        </button>
      </div>
    </div>

    <!-- Custom Bottom Sheet -->
    <div id="bottom-sheet-overlay" class="bottom-sheet-overlay"></div>
    <div id="bottom-sheet" class="bottom-sheet">
      <div class="bottom-sheet-handle"></div>
      <div id="bottom-sheet-content">${renderHelpBottomSheet()}</div>
    </div>

    <div
      id="video-container"
      class="absolute inset-0 rounded-lg overflow-hidden w-full h-full transition-all duration-300 ease-in-out hidden z-10"
      style="display: none;"
    >
      <div id="video-header" class="flex-shrink-0 z-30 pointer-events-none">
        <div class="flex items-center">
          <div class="w-8 h-8 mr-3 flex-shrink-0">
            <img src="/security.png" alt="Security" class="w-full h-full object-contain" />
          </div>
          <div>
            <h2>Security Check</h2>
            <p>Complete the challenge to verify you're human</p>
          </div>
        </div>
      </div>

      <div id="blur-border-overlay" class="relative flex-1 w-full">
        <div class="blur-border-edges blur-border-top"></div>
        <div class="blur-border-edges blur-border-bottom"></div>
        <div class="blur-border-edges blur-border-left"></div>
        <div class="blur-border-edges blur-border-right"></div>
        <div class="blur-border-corner blur-border-corner-tl"></div>
        <div class="blur-border-corner blur-border-corner-tr"></div>
        <div class="blur-border-corner blur-border-corner-bl"></div>
        <div class="blur-border-corner blur-border-corner-br"></div>
        <div
          id="interactivity-overlay"
          class="absolute inset-0 bg-transparent z-10 hidden cursor-not-allowed transition-opacity opacity-0"
        >
          <div class="flex items-center justify-center h-full w-full">
            <div class="loading-indicator px-4 py-2 rounded-xl flex items-center">
              <div class="animate-spin mr-3 h-5 w-5 text-blue-600">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  ></circle>
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              </div>
              <span class="text-sm font-medium text-gray-800">Processing...</span>
            </div>
          </div>
        </div>
      </div>
      <div id="help-trigger-container" class="w-full flex justify-center mt-4">
        <button
          id="help-trigger"
          class="text-[#7A757F] underline text-sm font-medium focus:outline-none"
        >
          Having Trouble?
        </button>
      </div>
    </div>
  </div>

  <script>
    const { pathname } = window.location;
    const agentPath = pathname.replace(/^\\/+|\\/+$/g, '');
    const socket = new WebSocket('${wsEndpoint}');

    let pc;
    let remoteStream;
    let video;
    let webRTCInitialized = false;
    let cropBoxBoundingRect = { width: 1366, height: 768, x: 0, y: 0 };
    let currentInputBoxRects = []; // Store current input overlay rectangles
    let currentFocusedOverlay = null; // Track currently focused input overlay

    let debugMode = true;
    let isVideoPaused = false;
    let frameRequestRetries = 0;
    const MAX_FRAME_REQUEST_RETRIES = 4;
    const FRAME_REQUEST_TIMEOUT = 3000; // 3 seconds
    let frameRequestTimer = null;
    let lastFrameReceived = 0;
    let frameReceiveCheckInterval = null;
    let inputChannel = null;
    let inputChannelReady = false; // Track input channel connection status

    /**
     * Request a new frame with retry logic for remote environments
     * Uses WebRTC input channel instead of WebSocket for direct peer-to-peer communication
     * Uses exponential backoff and multiple attempts to ensure frame generation
     */
    const requestFrameWithRetry = () => {
      if (!inputChannelReady || !inputChannel || inputChannel.readyState !== 'open') {
        console.warn('Input channel not ready for frame request, skipping');
        return;
      }

      // Clear any existing timer
      if (frameRequestTimer) {
        clearTimeout(frameRequestTimer);
        frameRequestTimer = null;
      }
      const attemptFrameRequest = (attempt = 0) => {
        if (attempt >= MAX_FRAME_REQUEST_RETRIES) {
          console.warn('Max frame request retries reached, giving up');
          frameRequestRetries = 0;
          return;
        }

        if (!inputChannelReady || !inputChannel || inputChannel.readyState !== 'open') {
          console.warn('Input channel became unavailable during retry, stopping');
          frameRequestRetries = 0;
          return;
        }

        console.log(
          '[kazeel] Requesting frame via WebRTC input channel (attempt ' +
          (attempt + 1) +
          '/' +
          MAX_FRAME_REQUEST_RETRIES +
          ') - peer-to-peer communication',
        );

        try {
          inputChannel.send(JSON.stringify({ type: 'request-frame' }));
          console.log('[kazeel] Frame request sent via WebRTC input channel');
        } catch (error) {
          console.error('[kazeel] Failed to send frame request via WebRTC:', error);
        }

        // Set timeout for next retry with exponential backoff
        const retryDelay = Math.min(FRAME_REQUEST_TIMEOUT * Math.pow(1.5, attempt), 5000);
        frameRequestTimer = setTimeout(() => {
          attemptFrameRequest(attempt + 1);
        }, retryDelay);
      };
      frameRequestRetries++;
      attemptFrameRequest();
    };
    /**
     * Stop frame requesting and clear any pending retries
     */
    const stopFrameRequesting = () => {
      if (frameRequestTimer) {
        clearTimeout(frameRequestTimer);
        frameRequestTimer = null;
      }
      frameRequestRetries = 0;
    };

    const updateVideoContainer = () => {
      if (!video) return;

      const videoContainer = document.getElementById('video-container');
      const blurBorderOverlay = document.getElementById('blur-border-overlay');
      const videoHeader = document.getElementById('video-header');
      const connectionFlow = document.getElementById('connection-flow');

      if (videoContainer && blurBorderOverlay && videoHeader) {
        const aspectRatio = cropBoxBoundingRect.width / cropBoxBoundingRect.height;
        const isMobile = window.innerWidth < 768;

        // Use the parent container dimensions
        const parentWidth = videoContainer.parentElement?.clientWidth || window.innerWidth;
        const parentHeight = videoContainer.parentElement?.clientHeight || window.innerHeight;

        const headerHeight = videoHeader.offsetHeight;

        let containerWidth, containerHeight;
        let minMargin = 20;
        let minContainerHeight = 150;
        containerWidth = parentWidth - minMargin;

        const availableHeight = parentHeight - headerHeight - minMargin;
        containerHeight = containerWidth / aspectRatio;

        if (containerHeight > availableHeight) {
          containerHeight = availableHeight;
          containerWidth = containerHeight * aspectRatio;
        }

        if (containerHeight < minContainerHeight) {
          containerHeight = minContainerHeight;
          containerWidth = containerHeight * aspectRatio;
        }

        const totalContainerHeight = containerHeight + headerHeight;

        // Apply dimensions with a small delay to ensure smooth transition
        requestAnimationFrame(() => {
          // The blur border overlay should account for the 20px padding on the video container
          const paddingOffset = 40;
          blurBorderOverlay.style.display = 'block';

          if (video) {
            video.style.objectFit = 'contain';
          }
        });

        console.log('Updated container and blur border dimensions:', {
          containerWidth: containerWidth,
          containerHeight: containerHeight,
          totalContainerHeight: totalContainerHeight,
          headerHeight: headerHeight,
          aspectRatio: aspectRatio,
          isMobile: isMobile,
          cropBox: cropBoxBoundingRect,
        });

        // Wait for animations to complete, then handle input overlays
        setTimeout(() => {
          const existingOverlays = document.querySelectorAll('.input-overlay');
          if (
            currentInputBoxRects &&
            currentInputBoxRects.length > 0 &&
            existingOverlays.length === 0
          ) {
            console.log(
              'Recreating input overlays after video container update (no existing overlays found)',
            );
            createInputOverlays(currentInputBoxRects);
          } else if (existingOverlays.length > 0) {
            console.log(
              'Input overlays already exist, repositioning after video container update',
            );
            // Reposition existing overlays after container update
            debouncedRepositionInputOverlays();
          }
        }, 300); // Wait for CSS transitions to complete
      }
    };

    /**
     * Handles focusing an input overlay and activating keyboard
     * @param {HTMLInputElement} overlay - The overlay element to focus
     */
    const focusInputOverlay = (overlay) => {
      if (currentFocusedOverlay && currentFocusedOverlay !== overlay) {
        blurInputOverlay(currentFocusedOverlay);
      }

      currentFocusedOverlay = overlay;
      overlay.focus();
    };

    /**
     * Handles blurring an input overlay and dismissing keyboard
     * @param {HTMLInputElement} overlay - The overlay element to blur
     */
    const blurInputOverlay = (overlay) => {
      if (!overlay) return;

      overlay.blur();

      if (currentFocusedOverlay === overlay) {
        currentFocusedOverlay = null;
      }
    };

    /**
     * Sets up global click listener to handle blur when clicking outside video and input overlays
     */
    const setupGlobalInputBlurListener = () => {
      if (window.globalInputBlurListener) {
        document.removeEventListener('click', window.globalInputBlurListener, true);
      }

      window.globalInputBlurListener = (e) => {
        const clickedOverlay = e.target.closest('.input-overlay');
        const clickedOnVideo = e.target === video || video?.contains(e.target);

        if (!clickedOverlay && !clickedOnVideo && currentFocusedOverlay) {
          blurInputOverlay(currentFocusedOverlay);
        }
      };

      document.addEventListener('click', window.globalInputBlurListener, true);
    };

    /**
     * Creates interactive input overlay elements for input fields.
     * Uses simplified input/beforeinput event approach for character-based transmission.
     * @param {Array} inputBoxRects - Array of input element rectangles
     */
    const createInputOverlays = (inputBoxRects) => {
      const existingOverlays = document.querySelectorAll('.input-overlay');
      if (existingOverlays.length > 0) {
        existingOverlays.forEach((overlay) => overlay.remove());
      }

      currentFocusedOverlay = null;

      if (!video || !inputBoxRects || inputBoxRects.length === 0) {
        return;
      }

      setupGlobalInputBlurListener();

      const videoContainer = document.getElementById('video-container');
      const blurBorderOverlay = document.getElementById('blur-border-overlay');
      const videoHeader = document.getElementById('video-header');

      if (!videoContainer || !blurBorderOverlay) {
        console.warn('Video container or blur border overlay not found');
        return;
      }

      const containerBounds = videoContainer.getBoundingClientRect();
      const videoBounds = video.getBoundingClientRect();
      const headerHeight = videoHeader ? videoHeader.offsetHeight : 0;

      inputBoxRects.forEach((inputRect, index) => {
        const overlayCoords = transformInputCoordsToVideoOverlay(inputRect, videoBounds);

        if (overlayCoords) {
          // Create actual input element for iOS Safari keyboard activation requirements
          const overlay = document.createElement('input');
          overlay.type = 'text';
          overlay.className = 'input-overlay';
          overlay.id = 'input-overlay-' + index;
          overlay.style.position = 'absolute';

          overlay.style.left = overlayCoords.x + 'px';
          overlay.style.top = overlayCoords.y + 'px';
          overlay.style.width = overlayCoords.width + 'px';
          overlay.style.height = overlayCoords.height + 'px';

          // Transparent styling for production invisibility
          overlay.style.backgroundColor = 'transparent';
          overlay.style.border = '1px solid rgba(0, 0, 0, 0.01)';
          overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.01)';
          overlay.style.color = 'rgba(0, 0, 0, 0.01)';
          overlay.style.caretColor = 'rgba(0, 0, 0, 0.01)';

          overlay.style.zIndex = '1000';
          overlay.style.pointerEvents = 'auto';
          overlay.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
          overlay.style.outline = 'none';
          overlay.style.fontSize = '16px'; // 16px prevents zoom on iOS Safari
          overlay.style.padding = '2px 8px';
          overlay.style.color = 'transparent';

          overlay.addEventListener('focus', () => {
            focusInputOverlay(overlay);
          });

          overlay.addEventListener('blur', () => {
            blurInputOverlay(overlay);
          });

          // Simplified input handling - process all input events immediately

          overlay.addEventListener('input', (e) => {
            const inputType = e.inputType;
            const data = e.data;
            const currentValue = e.target.value;

            // Clear the input immediately to prevent accumulation
            e.target.value = '';

            if (window.sendInput) {
              try {
                // Handle all character insertion types immediately
                if (inputType === 'insertText' && data) {
                  window.sendInput('char-input', {
                    text: data,
                    inputType: inputType,
                    timestamp: Date.now(),
                    source: 'input-event',
                  });
                } else if (inputType === 'insertCompositionText' && data) {
                  const lastChar = currentValue.charAt(currentValue.length - 1);
                  window.sendInput('char-input', {
                    text: lastChar,
                    inputType: inputType,
                    timestamp: Date.now(),
                    source: 'input-event-ime',
                  });
                } else if (inputType === 'insertFromPaste' && data) {
                  window.sendInput('text-insert', {
                    text: data,
                    inputType: inputType,
                    timestamp: Date.now(),
                    source: 'input-event-paste',
                  });
                } else if (inputType === 'deleteContentBackward') {
                  window.sendInput('char-input', {
                    text: '', // Backspace character
                    inputType: inputType,
                    timestamp: Date.now(),
                    source: 'input-event-backspace',
                  });
                } else if (inputType === 'deleteContentForward') {
                  window.sendInput('char-input', {
                    text: '', // Delete character
                    inputType: inputType,
                    timestamp: Date.now(),
                    source: 'input-event-delete',
                  });
                } else if (currentValue && currentValue.length > 0) {
                  // Only send the last character to prevent accumulation
                  const lastChar = currentValue.charAt(currentValue.length - 1);
                  window.sendInput('char-input', {
                    text: lastChar,
                    inputType: inputType || 'unknown',
                    timestamp: Date.now(),
                    source: 'input-event-fallback',
                  });
                }
              } catch (error) {
                console.error('Error sending input:', error);
              }
            } else {
              console.warn('sendInput not available');
            }
          });

          // Handle essential navigation keys only - character input handled by input events
          overlay.addEventListener('keydown', (e) => {
            const NAVIGATION_KEYS = [
              'Tab',
              'Escape',
              'ArrowUp',
              'ArrowDown',
              'ArrowLeft',
              'ArrowRight',
              'Backspace',
              'Home',
              'End',
              'PageUp',
              'PageDown',
              'F1',
              'F2',
              'F3',
              'F4',
              'F5',
              'F6',
              'F7',
              'F8',
              'F9',
              'F10',
              'F11',
              'F12',
            ];

            const isNavigationKey = NAVIGATION_KEYS.includes(e.key);
            const isModifierCombo = e.ctrlKey || e.metaKey || e.altKey;

            if (window.sendInput && (isNavigationKey || isModifierCombo)) {
              window.sendInput('navigation-key', {
                key: e.key,
                code: e.code,
                shiftKey: e.shiftKey,
                ctrlKey: e.ctrlKey,
                altKey: e.altKey,
                metaKey: e.metaKey,
                timestamp: Date.now(),
                virtualKeyCode: e.keyCode,
                source: 'navigation',
              });
            }
          });

          // Handle click events with coordinate transformation to remote browser
          overlay.addEventListener('click', (e) => {
            const overlayBounds = overlay.getBoundingClientRect();
            const relativeX = (e.clientX - overlayBounds.left) / overlayBounds.width;
            const relativeY = (e.clientY - overlayBounds.top) / overlayBounds.height;

            // Transform to remote browser coordinates
            const remoteX = inputRect.x + relativeX * inputRect.width;
            const remoteY = inputRect.y + relativeY * inputRect.height;

            if (window.sendInput) {
              window.sendInput('click', {
                x: Math.floor(remoteX),
                y: Math.floor(remoteY),
                button: 0,
              });
            }
          });

          // Handle touch events for mobile - iOS Safari keyboard activation requirements
          overlay.addEventListener('touchend', (e) => {
            if (e.changedTouches.length === 1) {
              const touch = e.changedTouches[0];

              // Calculate touch position and transform to remote browser coordinates
              const overlayBounds = overlay.getBoundingClientRect();
              const relativeX = (touch.clientX - overlayBounds.left) / overlayBounds.width;
              const relativeY = (touch.clientY - overlayBounds.top) / overlayBounds.height;

              const remoteX = inputRect.x + relativeX * inputRect.width;
              const remoteY = inputRect.y + relativeY * inputRect.height;

              if (window.sendInput) {
                window.sendInput('click', {
                  x: Math.floor(remoteX),
                  y: Math.floor(remoteY),
                  button: 0,
                });
              }
            }
          });

          blurBorderOverlay.appendChild(overlay);
        }
      });
    };

    /**
     * Transforms input element coordinates to video overlay coordinates
     * @param {Object} inputRect - Input element rectangle {x, y, width, height}
     * @param {DOMRect} videoBounds - Video element bounding rectangle
     * @returns {Object|null} Overlay coordinates or null if outside crop area
     */
    const transformInputCoordsToVideoOverlay = (inputRect, videoBounds) => {
      // Check if input is within the crop box
      if (
        inputRect.x < cropBoxBoundingRect.x ||
        inputRect.y < cropBoxBoundingRect.y ||
        inputRect.x + inputRect.width > cropBoxBoundingRect.x + cropBoxBoundingRect.width ||
        inputRect.y + inputRect.height > cropBoxBoundingRect.y + cropBoxBoundingRect.height
      ) {
        return null; // Input is outside the visible crop area
      }

      // Calculate relative position within crop box (0-1 range)
      const relativeX = (inputRect.x - cropBoxBoundingRect.x) / cropBoxBoundingRect.width;
      const relativeY = (inputRect.y - cropBoxBoundingRect.y) / cropBoxBoundingRect.height;
      const relativeWidth = inputRect.width / cropBoxBoundingRect.width;
      const relativeHeight = inputRect.height / cropBoxBoundingRect.height;

      // Get video container positioning context
      const videoContainer = document.getElementById('video-container');
      const blurBorderOverlay = document.getElementById('blur-border-overlay');

      if (!videoContainer || !blurBorderOverlay) {
        console.warn('Video container elements not found for coordinate transformation');
        return null;
      }

      // Get the actual video display area (accounting for object-fit: contain)
      const containerBounds = blurBorderOverlay.getBoundingClientRect();
      const videoAspectRatio = cropBoxBoundingRect.width / cropBoxBoundingRect.height;
      const containerAspectRatio = containerBounds.width / containerBounds.height;

      let videoDisplayWidth, videoDisplayHeight, videoOffsetX, videoOffsetY;

      if (videoAspectRatio > containerAspectRatio) {
        // Video is wider than container - letterboxed top/bottom
        videoDisplayWidth = containerBounds.width;
        videoDisplayHeight = containerBounds.width / videoAspectRatio;
        videoOffsetX = 0;
        videoOffsetY = (containerBounds.height - videoDisplayHeight) / 2;
      } else {
        // Video is taller than container - letterboxed left/right
        videoDisplayWidth = containerBounds.height * videoAspectRatio;
        videoDisplayHeight = containerBounds.height;
        videoOffsetX = (containerBounds.width - videoDisplayWidth) / 2;
        videoOffsetY = 0;
      }

      // Transform to video overlay coordinates within the display area
      return {
        x: videoOffsetX + relativeX * videoDisplayWidth,
        y: videoOffsetY + relativeY * videoDisplayHeight,
        width: relativeWidth * videoDisplayWidth,
        height: relativeHeight * videoDisplayHeight,
      };
    };

    const initWebRTC = () => {
      pc = new RTCPeerConnection({
        iceServers: [
          {
            urls: 'stun:stun.cloudflare.com:3478',
          },
          {
            urls: 'turn:relay1.expressturn.com:3478',
            username: 'ef89RMU4SHUQMSOUU9',
            credential: 'jvkMMnQxWX4Qrhe3',
          },
        ],
      });

      pc.ondatachannel = (event) => {
        inputChannel = event.channel;

        window.sendInput = (type, payload) => {
          if (inputChannel && inputChannel.readyState === 'open') {
            inputChannel.send(JSON.stringify({ type, ...payload }));
          } else {
            console.warn('Input channel not ready for sending input');
          }
        };

        inputChannel.onopen = () => {
          console.log(
            '[kazeel] Input channel ready to send - WebRTC peer-to-peer communication established',
          );
          inputChannelReady = true;
          requestFrameWithRetry();
        };

        inputChannel.onclose = () => {
          console.log('Input channel closed');
          inputChannelReady = false;
        };

        inputChannel.onerror = (error) => {
          console.error('Input channel error:', error);
          inputChannelReady = false;
        };

        // Handle incoming messages from the remote peer (screen-cropper)
        inputChannel.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
          } catch (err) {
            console.error('Failed to parse data channel message:', err);
          }
        };
      };

      remoteStream = new MediaStream();
      if (!video) {
        video = document.createElement('video');
      }
      video.id = 'remoteVideo';
      video.autoplay = true;
      video.playsInline = true;
      video.muted = true;
      video.setAttribute('playsinline', '');
      video.srcObject = remoteStream;

      // Apply styles
      Object.assign(video.style, {
        width: '100%',
        objectFit: 'contain',
        display: 'block',
        position: 'relative',
        touchAction: 'manipulation',
        transition: 'all 300ms ease-in-out',
      });

      const blurBorderOverlay = document.getElementById('blur-border-overlay');
      blurBorderOverlay.insertBefore(video, blurBorderOverlay.firstChild);

      updateVideoContainer();

      pc.onicecandidate = (event) => {
        if (event.candidate) {
          socket.send(JSON.stringify({ type: 'candidate', candidate: event.candidate }));
        }
      };

      pc.ontrack = (e) => {
        console.log('Received remote track');
        e.streams[0].getTracks().forEach((track) => remoteStream.addTrack(track));
      };

      setupInputListeners();
    };

    const pauseVideo = () => {
      if (isVideoPaused || !video) return;

      console.log('Pausing video stream');

      try {
        video.pause();
      } catch (err) {
        console.warn('Error pausing video:', err);
      }

      const overlay = document.getElementById('interactivity-overlay');
      if (overlay) {
        overlay.classList.remove('hidden');
        overlay.style.opacity = '1';
      }
      video.style.pointerEvents = 'none';
      isVideoPaused = true;
    };

    const resumeVideo = () => {
      if (!isVideoPaused || !video) return;

      console.log('Resuming video stream');

      video.play().catch((err) => {
        console.warn('Error playing video:', err);
      });

      const overlay = document.getElementById('interactivity-overlay');
      if (overlay) {
        overlay.style.opacity = '0';
        setTimeout(() => {
          overlay.classList.add('hidden');
        }, 300);
      }
      updateVideoContainer();
      video.style.pointerEvents = 'auto';
      isVideoPaused = false;
    };

    const showVideoContainer = () => {
      const videoContainer = document.getElementById('video-container');
      const connectionFlow = document.getElementById('connection-flow-wrapper');

      if (videoContainer && connectionFlow) {
        connectionFlow.style.display = 'none';
        videoContainer.style.display = 'flex';
        videoContainer.style.flexDirection = 'column';
        videoContainer.classList.remove('hidden');
        updateVideoContainer();
      }
    };

    const hideVideoContainer = () => {
      const videoContainer = document.getElementById('video-container');
      const connectionFlow = document.getElementById('connection-flow-wrapper');

      if (videoContainer && connectionFlow) {
        videoContainer.style.display = 'none';
        videoContainer.classList.add('hidden');
        connectionFlow.style.display = 'flex';
      }
    };

    /**
     * Restores the UI to its initial state when interactivity is completed
     * This includes hiding the video container, showing the connection flow,
     * cleaning up WebRTC connections, and removing input overlays
     */
    const restoreUIToInitialState = () => {
      console.log('[kazeel] Restoring UI to initial state');

      stopFrameRequesting();

      const existingOverlays = document.querySelectorAll('.input-overlay');
      existingOverlays.forEach((overlay) => overlay.remove());
      currentInputBoxRects = [];
      currentFocusedOverlay = null;

      if (window.globalInputBlurListener) {
        document.removeEventListener('click', window.globalInputBlurListener, true);
        window.globalInputBlurListener = null;
      }

      if (video) {
        video.pause();
        video.srcObject = null;
        if (video.parentNode) {
          video.parentNode.removeChild(video);
        }
        video = null;
      }

      if (pc) {
        pc.close();
        pc = null;
      }

      if (remoteStream) {
        remoteStream.getTracks().forEach((track) => track.stop());
        remoteStream = null;
      }

      if (window.sendInput) {
        window.sendInput = null;
      }

      webRTCInitialized = false;
      isVideoPaused = false;
      frameRequestRetries = 0;
      lastFrameReceived = 0;
      cropBoxBoundingRect = { width: 1366, height: 768, x: 0, y: 0 };
      inputChannel = null;
      inputChannelReady = false;

      if (frameReceiveCheckInterval) {
        clearInterval(frameReceiveCheckInterval);
        frameReceiveCheckInterval = null;
      }
      hideVideoContainer();
    };

    /**
     * Checks if a click event is within any input overlay area
     * @param {MouseEvent|Touch} e - The click/touch event
     * @returns {boolean} True if click is within an input overlay area
     */
    const isClickWithinInputOverlayArea = (e) => {
      if (!currentInputBoxRects || currentInputBoxRects.length === 0) {
        return false;
      }

      const videoBounds = video.getBoundingClientRect();
      if (!videoBounds) {
        return false;
      }

      // Get click coordinates relative to video
      const clickX = e.clientX - videoBounds.left;
      const clickY = e.clientY - videoBounds.top;

      // Check if click is within any input overlay area
      for (const inputRect of currentInputBoxRects) {
        const overlayCoords = transformInputCoordsToVideoOverlay(inputRect, videoBounds);
        if (overlayCoords) {
          // Check if click is within this overlay area
          if (
            clickX >= overlayCoords.x &&
            clickX <= overlayCoords.x + overlayCoords.width &&
            clickY >= overlayCoords.y &&
            clickY <= overlayCoords.y + overlayCoords.height
          ) {
            return true;
          }
        }
      }
      return false;
    };

    const setupInputListeners = () => {
      let isDragging = false; // Track drag state
      let lastMousePosition = { x: 0, y: 0 }; // Store last position for drag termination
      let lastMoveTime = 0; // For debouncing mousemove
      const MOVE_DEBOUNCE_MS = 10; // Send mousemove every 10ms

      const sendMouseEvent = (type, e) => {
        let clientX, clientY;

        if (e instanceof MouseEvent || e instanceof Touch) {
          clientX = e.clientX;
          clientY = e.clientY;
        } else {
          console.warn('Unknown event type in sendMouseEvent');
          return;
        }

        const bounds = video.getBoundingClientRect();

        // Ensure coordinates are within bounds
        if (
          clientX < bounds.left ||
          clientX > bounds.right ||
          clientY < bounds.top ||
          clientY > bounds.bottom
        ) {
          console.log('Ignoring event outside video bounds');
          return;
        }

        const relativeX = (clientX - bounds.left) / bounds.width;
        const relativeY = (clientY - bounds.top) / bounds.height;

        const scaledX = Math.floor(relativeX * cropBoxBoundingRect.width);
        const scaledY = Math.floor(relativeY * cropBoxBoundingRect.height);
        const translatedX = scaledX + cropBoxBoundingRect.x;
        const translatedY = scaledY + cropBoxBoundingRect.y;

        // Update last position for drag termination
        lastMousePosition = { x: translatedX, y: translatedY };

        if (
          window.sendInput &&
          (type !== 'mousemove' || Date.now() - lastMoveTime >= MOVE_DEBOUNCE_MS)
        ) {
          window.sendInput(type, { x: translatedX, y: translatedY, button: e.button || 0 });
          if (type === 'mousemove') {
            lastMoveTime = Date.now();
          }
        } else if (!window.sendInput) {
          console.warn('Input channel not ready');
        }
      };

      const interactivityOverlay = document.getElementById('interactivity-overlay');
      if (interactivityOverlay) {
        interactivityOverlay.onclick = (e) => {
          e.preventDefault();
          e.stopPropagation();
          console.log('Click blocked during processing');
          return false;
        };
        // Prevent drag events from being blocked by overlay
        interactivityOverlay.onmousedown = (e) => {
          e.preventDefault();
          e.stopPropagation();
          return false;
        };
        interactivityOverlay.ontouchstart = (e) => {
          e.preventDefault();
          e.stopPropagation();
          return false;
        };
      }

      /**
       * Enhanced video click handler that blurs input overlays when clicking on empty video areas
       */
      video.onclick = (e) => {
        // Check if click is within any input overlay area
        const clickedOnInputOverlay = isClickWithinInputOverlayArea(e);

        if (!clickedOnInputOverlay && currentFocusedOverlay) {
          console.log(
            '🔴 [INPUT OVERLAY] Click on empty video area, blurring focused overlay',
          );
          blurInputOverlay(currentFocusedOverlay);
        }
      };

      // Mouse events for desktop
      video.onmousedown = (e) => {
        isDragging = true;
        sendMouseEvent('mousedown', e);
      };

      video.onmousemove = (e) => {
        if (isDragging) {
          sendMouseEvent('mousemove', e);
        }
      };

      video.onmouseup = (e) => {
        if (isDragging) {
          sendMouseEvent('mouseup', e);
          isDragging = false;
        }
        // Send click only for non-drag interactions
        if (!isDragging) {
        }
      };

      video.onmouseleave = () => {
        if (isDragging) {
          sendMouseEvent('mouseup', lastMousePosition);
          isDragging = false;
        }
      };

      // Touch events for mobile
      let touchStartTime = 0;
      let lastTouchX = 0;
      let lastTouchY = 0;
      const CLICK_DELAY_THRESHOLD = 300;
      const MOVE_THRESHOLD = 10;

      video.addEventListener(
        'touchstart',
        (e) => {
          e.preventDefault();
          if (e.touches.length === 1) {
            e.preventDefault(); // Prevent scrolling
            const touch = e.touches[0];
            lastTouchX = touch.clientX;
            lastTouchY = touch.clientY;
            touchStartTime = Date.now();
            isDragging = true;
            sendMouseEvent('mousedown', touch);
          }
        },
        { passive: false },
      );

      video.addEventListener(
        'touchmove',
        (e) => {
          e.preventDefault();
          if (e.touches.length === 1 && isDragging) {
            const touch = e.touches[0];
            const deltaX = Math.abs(touch.clientX - lastTouchX);
            const deltaY = Math.abs(touch.clientY - lastTouchY);

            if (deltaX > MOVE_THRESHOLD || deltaY > MOVE_THRESHOLD) {
              lastTouchX = touch.clientX;
              lastTouchY = touch.clientY;
              sendMouseEvent('mousemove', touch);
            }
          }
        },
        { passive: false },
      );

      video.addEventListener(
        'touchend',
        (e) => {
          e.preventDefault();
          const touchDuration = Date.now() - touchStartTime;

          if (e.changedTouches.length === 1 && isDragging) {
            const touch = e.changedTouches[0];
            sendMouseEvent('mouseup', touch);
            const touchDuration = Date.now() - touchStartTime;
            const deltaX = Math.abs(touch.clientX - lastTouchX);
            const deltaY = Math.abs(touch.clientY - lastTouchY);

            // Send click only for short, non-dragging touches
            if (
              touchDuration < CLICK_DELAY_THRESHOLD &&
              deltaX < MOVE_THRESHOLD &&
              deltaY < MOVE_THRESHOLD
            ) {
              // Check if touch is within any input overlay area
              const touchedOnInputOverlay = isClickWithinInputOverlayArea(touch);

              if (!touchedOnInputOverlay && currentFocusedOverlay) {
                console.log(
                  '🔴 [INPUT OVERLAY] Touch on empty video area, blurring focused overlay',
                );
                blurInputOverlay(currentFocusedOverlay);
              }
            }
            isDragging = false;
          }
        },
        { passive: true },
      );

      video.addEventListener(
        'touchcancel',
        () => {
          if (isDragging) {
            sendMouseEvent('mouseup', lastMousePosition);
            isDragging = false;
          }
        },
        { passive: false },
      );

      video.addEventListener(
        'touchcancel',
        (e) => {
          e.preventDefault();
          touchStartTime = 0;
          lastTouchX = 0;
          lastTouchY = 0;
        },
        { passive: false },
      );

      // Video keyboard events removed - all input handled by input overlays with modern input events
    };

    const handleSocketMessage = async (event) => {
      try {
        const msg = JSON.parse(event.data);
        if (msg.type === 'offer') {
          console.log('Received offer', msg);
          await pc.setRemoteDescription(new RTCSessionDescription(msg.offer));
          const answer = await pc.createAnswer();
          await pc.setLocalDescription(answer);
          socket.send(JSON.stringify({ type: 'answer', answer }));
        } else if (msg.type === 'candidate') {
          console.log('Received ICE candidate', msg);
          await pc.addIceCandidate(new RTCIceCandidate(msg.candidate));
        } else if (msg.type === 'answer') {
          console.log('Received answer', msg);
          await pc.setRemoteDescription(new RTCSessionDescription(msg.answer));
        } else if (msg.type === 'interactivity-status') {
          if (msg.status === 'paused') {
            console.log('Pausing interactivity', cropBoxBoundingRect);
            pauseVideo();
          } else if (msg.status === 'completed') {
            console.log('Interactivity completed - restoring UI to initial state');
            restoreUIToInitialState();
          } else if (msg.status === 'enabled') {
            console.log('Resuming interactivity', msg.cropBox);
            cropBoxBoundingRect = msg.cropBox;

            // Update input overlays if provided
            if (msg.inputBoxRects) {
              currentInputBoxRects = msg.inputBoxRects;
              console.log('Received input box rects:', currentInputBoxRects);
            }

            if (video) {
              updateVideoContainer();

              // Create input overlays after video container is updated (to avoid race condition)
              if (msg.inputBoxRects && currentInputBoxRects.length > 0) {
                setTimeout(() => {
                  console.log('Creating input overlays after video container update');
                  createInputOverlays(currentInputBoxRects);
                }, 300); // Wait for CSS transitions to complete
              }
            }

            if (!webRTCInitialized) {
              webRTCInitialized = true;
              initWebRTC();
              socket.send(JSON.stringify({ type: 'ready' }));

              setTimeout(() => {
                try {
                  console.log('Showing video container');
                  showVideoContainer();
                } catch (err) {
                  console.error('Error showing video container:', err);
                }
              }, 500);
            } else {
              resumeVideo();
              requestFrameWithRetry();
              showVideoContainer();
            }
          }
        }
      } catch (e) {
      }
    };

    socket.addEventListener('open', () => {
      console.log('WebSocket connected and ready to receive trigger.');
    });

    socket.addEventListener('message', handleSocketMessage);

    document.addEventListener('retry-request', () => {
      if (socket && socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify({ type: 'retry' }));
      }
    });

    /**
     * Repositions existing input overlays when video container layout changes
     * This is crucial for mobile keyboard appearance/dismissal
     */
    let repositionTimeout = null;
    const repositionInputOverlays = () => {
      const existingOverlays = document.querySelectorAll('.input-overlay');
      if (
        existingOverlays.length === 0 ||
        !currentInputBoxRects ||
        currentInputBoxRects.length === 0
      ) {
        return;
      }

      console.log(
        '📱 [INPUT OVERLAY] Repositioning',
        existingOverlays.length,
        'overlays for layout change',
      );

      const videoBounds = video ? video.getBoundingClientRect() : null;
      if (!videoBounds) {
        console.warn('❌ [INPUT OVERLAY] Cannot reposition - video bounds not available');
        return;
      }

      existingOverlays.forEach((overlay, index) => {
        const inputRect = currentInputBoxRects[index];
        if (inputRect) {
          const overlayCoords = transformInputCoordsToVideoOverlay(inputRect, videoBounds);
          if (overlayCoords) {
            // Update overlay position
            overlay.style.left = overlayCoords.x + 'px';
            overlay.style.top = overlayCoords.y + 'px';
            overlay.style.width = overlayCoords.width + 'px';
            overlay.style.height = overlayCoords.height + 'px';

            console.log('📍 [INPUT OVERLAY] Repositioned overlay', overlay.id, 'to:', {
              x: overlayCoords.x,
              y: overlayCoords.y,
              width: overlayCoords.width,
              height: overlayCoords.height,
            });
          }
        }
      });
    };

    /**
     * Debounced repositioning to handle rapid layout changes
     */
    const debouncedRepositionInputOverlays = () => {
      if (repositionTimeout) {
        clearTimeout(repositionTimeout);
      }
      repositionTimeout = setTimeout(() => {
        repositionInputOverlays();
      }, 300); // Wait for CSS transitions to complete
    };

    /**
     * Manages input overlay active state to prevent transform conflicts
     */
    const setInputOverlayActiveState = (active) => {
      const videoContainer = document.getElementById('video-container');
      if (videoContainer) {
        if (active) {
          videoContainer.classList.add('input-overlay-active');
        } else {
          videoContainer.classList.remove('input-overlay-active');
        }
      }
    };

    // Initial check for mobile orientation
    if (window.innerWidth < 768) {
      console.log('Mobile device detected, optimizing layout');
    }

    // Add viewport change listeners for mobile keyboard and orientation changes
    let resizeTimeout = null;
    const handleViewportChange = () => {
      if (resizeTimeout) {
        clearTimeout(resizeTimeout);
      }
      resizeTimeout = setTimeout(() => {
        console.log('📱 [VIEWPORT] Viewport changed, repositioning overlays');
        debouncedRepositionInputOverlays();
      }, 300); // Wait for viewport changes to settle
    };

    // Listen for window resize (includes mobile keyboard appearance/dismissal)
    window.addEventListener('resize', handleViewportChange);

    // Listen for orientation changes on mobile
    window.addEventListener('orientationchange', () => {
      setTimeout(handleViewportChange, 300); // iOS needs extra delay after orientation change
    });

    // Listen for visual viewport changes (mobile keyboard)
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleViewportChange);
    }

    // Show/hide the help trigger based on step (not on first step)
    function updateHelpTriggerVisibility() {
      const helpTrigger = document.getElementById('help-trigger');
      if (!helpTrigger) return;

      const main = document.getElementById('connection-flow');
      const hasContent = main.innerText.trim() !== '';

      if (
        !hasContent ||
        (main &&
          main.innerText.match(/Agree and Continue|Terms and Conditions|Kazeel will connect/))
      ) {
        helpTrigger.style.display = 'none';
      } else {
        helpTrigger.style.display = 'block';
      }
    }

    // Call on load and on main content update
    setTimeout(updateHelpTriggerVisibility, 100);
    const observer = new MutationObserver(updateHelpTriggerVisibility);
    observer.observe(document.getElementById('connection-flow'), {
      childList: true,
      subtree: true,
    });

    // Custom Bottom Sheet Logic
    const bottomSheetOverlay = document.getElementById('bottom-sheet-overlay');
    const bottomSheet = document.getElementById('bottom-sheet');
    const helpTrigger = document.getElementById('help-trigger');

    helpTrigger?.addEventListener('click', function() {
      bottomSheetOverlay.classList.add('active');
      bottomSheet.classList.add('active');
    });

    bottomSheetOverlay?.addEventListener('click', function() {
      closeBottomSheet();
    });

    function closeBottomSheet() {
      bottomSheetOverlay.classList.remove('active');
      bottomSheet.classList.remove('active');
    }

    document.getElementById('sheet-restart')?.addEventListener('click', function() {
      document.dispatchEvent(new CustomEvent('retry-request'));
      closeBottomSheet();
    });

    document.getElementById('sheet-support')?.addEventListener('click', function() {
      window.open('https://kazeel.com/support', '_blank');
    });

    document.getElementById('sheet-back')?.addEventListener('click', function() {
      closeBottomSheet();
    });
  </script>
  </body>
  </html>`;
};

export const DashboardLayoutCard = (
  userId: string,
  linkId: string,
  serviceId: string,
  serviceName: string,
) => {
  return html` <!DOCTYPE html>
  <html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <title>Connect to ${serviceName} - Kazeel</title>
    <script src="https://unpkg.com/htmx.org@2.0.4"></script>
    <script src="https://unpkg.com/htmx.org/dist/ext/ws.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@plainsheet/core@latest/dist/plainsheet-core.umd.js"></script>
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/video-layout.css" />
    <link rel="stylesheet" href="/css/bottom-sheet.css" />
  </head>
  <body
    class="light bg-neutral-60 dark:bg-primary-50 h-screen flex items-center justify-center"
  >
  <div class="relative md:w-[435px]  md:rounded-xl shadow-md shadow-neutral-50 bg-surface">
    <div
      id="connection-flow-wrapper"
      class="h-screen flex flex-col justify-center items-center relative md:w-[435px] md:h-auto md:rounded-xl shadow-md shadow-neutral-50 bg-surface"
    >
      <div
        class="w-screen flex flex-col items-center justify-center bg-surface text-surface-on-surface md:h-[auto] md:w-[435px] md:rounded-xl"
      >
        ${renderTermsAndConditions(userId, linkId, serviceId)}
      </div>
  </body>
  </html>`;
};

function renderTermsAndConditions(
  userId: string,
  linkId: string,
  platform: string,
): HTMLReturnType {
  const platformInfo = platformDetails[platform];

  if (!platformInfo) {
    throw Error('Platform not supported');
  }

  return html`
    <div class="p-6">
      ${KazeelPlatformLogo({
        platformLogo: platformInfo.logo,
        platformName: platformInfo.name,
        variant: 'overlapping',
      })}

      <div class="p-2 text-center">
        <h2 class="text-xl font-semibold my-4">Kazeel will connect you with ${platformInfo.name}</h2>
      </div>
      <div class="p-2">
        <div class="space-y-4">
          <div class="flex items-start">
            <div class="bg-[#ECE7EB] p-2 rounded-full mr-3 flex-shrink-0">
              <svg
                width="13"
                height="15"
                viewBox="0 0 13 15"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                class="size-6"
              >
                <path
                  d="M5.43717 9.6263H7.56217L7.15488 7.34193C7.39099 7.22387 7.57693 7.05269 7.7127 6.82839C7.84846 6.60408 7.91634 6.35616 7.91634 6.08464C7.91634 5.69505 7.77763 5.36155 7.5002 5.08411C7.22276 4.80668 6.88926 4.66797 6.49967 4.66797C6.11009 4.66797 5.77658 4.80668 5.49915 5.08411C5.22172 5.36155 5.08301 5.69505 5.08301 6.08464C5.08301 6.35616 5.15089 6.60408 5.28665 6.82839C5.42242 7.05269 5.60836 7.22387 5.84447 7.34193L5.43717 9.6263ZM6.49967 14.5846C4.8587 14.1714 3.50401 13.2299 2.43561 11.7602C1.36721 10.2904 0.833008 8.65825 0.833008 6.8638V2.54297L6.49967 0.417969L12.1663 2.54297V6.8638C12.1663 8.65825 11.6321 10.2904 10.5637 11.7602C9.49533 13.2299 8.14065 14.1714 6.49967 14.5846ZM6.49967 13.0971C7.72745 12.7076 8.74273 11.9284 9.54551 10.7596C10.3483 9.59089 10.7497 8.29227 10.7497 6.8638V3.51693L6.49967 1.92318L2.24967 3.51693V6.8638C2.24967 8.29227 2.65106 9.59089 3.45384 10.7596C4.25662 11.9284 5.2719 12.7076 6.49967 13.0971Z"
                  fill="#1C1B1F"
                />
              </svg>
            </div>
            <div>
              <h3 class="font-medium text-[#49454E] text-sm">Provide your credentials</h3>
              <p class="text-gray-600 text-xs">We'll guide you step by step as you sign in</p>
            </div>
          </div>

          <div class="flex items-start">
            <div class="bg-[#ECE7EB] p-2 rounded-full mr-3 flex-shrink-0">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-6"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M9 17.25v1.007a3 3 0 0 1-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0 1 15 18.257V17.25m6-12V15a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 15V5.25m18 0A2.25 2.25 0 0 0 18.75 3H5.25A2.25 2.25 0 0 0 3 5.25m18 0V12a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 12V5.25"
                />
              </svg>
            </div>
            <div>
              <h3 class="font-medium text-[#49454E] text-sm">Real-Time Transparency</h3>
              <p class="text-gray-600 text-xs">
                Use "Live View" to watch our secure connection process as it happens
              </p>
            </div>
          </div>

          <div class="flex items-start">
            <div class="bg-[#ECE7EB] p-2 rounded-full mr-3 flex-shrink-0">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-6"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M15 12H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                />
              </svg>
            </div>
            <div>
              <h3 class="font-medium text-[#49454E] text-sm">Disable Anytime</h3>
              <p class="text-gray-600 text-xs">
                You're always in control—turn off this connection in your Kazeel settings anytime
              </p>
            </div>
          </div>

          <div class="flex items-start">
            <div class="bg-[#ECE7EB] p-2 rounded-full mr-3 flex-shrink-0">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-6"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"
                />
              </svg>
            </div>
            <div>
              <h3 class="font-medium text-[#49454E] text-sm">Protecting Your Privacy</h3>
              <p class="text-gray-600 text-xs">
                We securely store your authentication and protect your privacy
              </p>
            </div>
          </div>
        </div>
        <p class="my-6 text-xs text-gray-500 text-center">
          <strong>Disclaimer:</strong> Kazeel is an independent application and is not affiliated
          with, endorsed by, or sponsored by any third parties or their trademarks, logos, or
          copyrights.
        </p>
      </div>
      <div class="flex flex-col space-y-3">
        <button
          class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-700 w-full dark:bg-primary-700 dark:hover:bg-primary-600"
          hx-get="/${linkId}/${platform}/flow?userId=${userId}"
          hx-push-url="true"
          hx-target="body"
          hx-swap="outerHTML"
        >
          Agree and Continue
        </button>
        <button
          class="border border-gray-400 text-gray-700 px-4 py-2 rounded-lg w-full dark:border-primary-900 dark:text-primary-900 font-medium"
        >
          Decline
        </button>
      </div>
      <div class="mt-14 flex items-center justify-center">
        <a href="asda" class="text-xs text-[#7A757F] underline">Privacy Policy</a>
        <span class="text-xs text-[#7A757F] mx-[2px]">•</span>
        <a href="asda" class="text-xs text-[#7A757F] underline">English (United States)</a>
      </div>
    </div>`;
}
