# Comprehensive Error Handling System

This document provides a complete guide to the error handling system implemented for the Cloudflare Workers API using Durable Objects.

## Overview

The error handling system provides:

1. **Custom Error Classification**: Structured error hierarchy with semantic meaning
2. **RPC Error Transformation**: Automatic conversion of generic RPC errors into typed errors
3. **Localized Error Messages**: Multi-language support with user-friendly messages
4. **API Error Middleware**: Consistent error responses across all endpoints
5. **HTTP Status Code Mapping**: Proper status codes for different error types

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   API Request   │───▶│  Error Middleware │───▶│  Error Response │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │ RPC Error        │
                       │ Transformer      │
                       └──────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │ Custom Error     │
                       │ Classes          │
                       └──────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │ Localization     │
                       │ System           │
                       └──────────────────┘
```

## Error Classes

### Base Error Class

All API errors extend `BaseApiError`:

```typescript
import { BaseApiError, ErrorCategory, HttpStatusCode } from '../common/error';

class CustomError extends BaseApiError {
  constructor(message: string) {
    super(message, {
      category: ErrorCategory.VALIDATION,
      statusCode: HttpStatusCode.BAD_REQUEST,
      errorCode: 'CUSTOM_ERROR',
      technicalDetails: { /* ... */ },
      context: { /* ... */ }
    });
  }
}
```

### Available Error Classes

- **ValidationError** (400): Input validation failures
- **AuthenticationError** (401): Authentication issues
- **AuthorizationError** (403): Permission denied
- **NotFoundError** (404): Resource not found
- **ConflictError** (409): Resource conflicts
- **RateLimitError** (429): Rate limiting
- **RpcError** (502): Durable Object RPC failures
- **InternalServerError** (500): Internal system errors
- **ExternalServiceError** (502): External service issues
- **NetworkError** (503): Network connectivity problems

## Usage Examples

### 1. Basic Validation Errors

```typescript
import { ValidationError } from '../common/error';

// Missing field
if (!userId) {
  throw ValidationError.missingField('userId');
}

// Invalid format
if (!email.includes('@')) {
  throw ValidationError.invalidFormat('email', 'valid email address');
}

// Invalid value
if (!['facebook', 'google'].includes(platform)) {
  throw ValidationError.invalidValue('platform', platform, ['facebook', 'google']);
}
```

### 2. RPC Error Handling

```typescript
import { handleRpcCall } from '../common/error';

// Automatic RPC error transformation
const result = await handleRpcCall(
  'CoordinatorDO',
  'createLink',
  () => coordinatorStub.createLink(platform, userId),
  { userId, platform }
);

// Manual RPC error transformation
import { RpcErrorTransformer } from '../common/error';

try {
  const result = await coordinatorStub.createLink(platform, userId);
} catch (error) {
  throw RpcErrorTransformer.transform(error, {
    doName: 'CoordinatorDO',
    method: 'createLink',
    userId,
    platform
  });
}
```

### 3. Authentication and Authorization

```typescript
import { AuthenticationError, AuthorizationError } from '../common/error';

// Authentication errors
if (!authToken) {
  throw AuthenticationError.missingToken();
}

if (isTokenExpired(authToken)) {
  throw AuthenticationError.sessionExpired();
}

// Authorization errors
if (!hasPermission(user, 'admin')) {
  throw AuthorizationError.insufficientPermissions('admin panel');
}
```

### 4. Resource Management

```typescript
import { NotFoundError, ConflictError } from '../common/error';

// Not found errors
const link = await getLinkById(linkId);
if (!link) {
  throw NotFoundError.link(linkId);
}

// Conflict errors
if (user.isConnectedTo(platform)) {
  throw ConflictError.alreadyConnected(platform);
}
```

### 5. Rate Limiting

```typescript
import { RateLimitError } from '../common/error';

if (retryCount >= MAX_RETRIES) {
  throw RateLimitError.retryLimitExceeded(platform, retryCount);
}
```

## Middleware Integration

### Basic Setup

```typescript
import { Hono } from 'hono';
import { getErrorMiddleware } from '../common/error';

const app = new Hono();

// Add error middleware (automatically detects environment)
app.use('*', (c, next) => {
  const environment = c.env.ENVIRONMENT || 'production';
  return getErrorMiddleware(environment)(c, next);
});
```

### Environment-Specific Middleware

```typescript
import { 
  developmentErrorMiddleware, 
  productionErrorMiddleware 
} from '../common/error';

// Development: includes technical details and stack traces
app.use('*', developmentErrorMiddleware());

// Production: minimal error information for security
app.use('*', productionErrorMiddleware());
```

### Custom Middleware Configuration

```typescript
import { createApiErrorMiddleware } from '../common/error';

app.use('*', createApiErrorMiddleware({
  includeTechnicalDetails: true,
  defaultLocale: 'pt',
  transformRpcErrors: true,
  logger: (error, context) => {
    // Custom logging logic
    console.log('Custom error log:', error.toJSON());
  }
}));
```

## Error Response Format

All errors return a consistent JSON structure:

```json
{
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "The provided data is invalid",
    "category": "validation",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "path": "/api/create-link",
    "method": "POST",
    "statusCode": 400,
    "context": {
      "field": "serviceId",
      "value": "invalid-platform"
    },
    "details": {
      "technicalDetails": "Only included in development"
    }
  }
}
```

## Localization

The system supports multiple languages (English, Portuguese, Spanish):

```typescript
// Error messages are automatically localized based on Accept-Language header
// English (default)
"VALIDATION_FAILED": "The provided data is invalid"

// Portuguese
"VALIDATION_FAILED": "Os dados fornecidos são inválidos"

// Spanish
"VALIDATION_FAILED": "Los datos proporcionados son inválidos"
```

### Adding New Error Codes

1. Add the error code to `localizationMessages.ts`:

```typescript
export const localizationMessages = {
  en: {
    NEW_ERROR_CODE: 'Error message in English',
    // ...
  },
  pt: {
    NEW_ERROR_CODE: 'Mensagem de erro em português',
    // ...
  },
  es: {
    NEW_ERROR_CODE: 'Mensaje de error en español',
    // ...
  }
};
```

2. Use the error code in your custom error:

```typescript
throw new ValidationError(
  'Technical message for logs',
  'NEW_ERROR_CODE',
  { technicalDetails },
  { userContext }
);
```

## Best Practices

### 1. Use Specific Error Types

```typescript
// ❌ Generic error
throw new Error('Something went wrong');

// ✅ Specific error type
throw ValidationError.missingField('userId');
```

### 2. Provide Context

```typescript
// ❌ No context
throw new NotFoundError('Resource not found');

// ✅ With context
throw NotFoundError.link(linkId);
```

### 3. Handle RPC Calls Properly

```typescript
// ❌ No error transformation
const result = await durableObjectStub.method();

// ✅ With error transformation
const result = await handleRpcCall(
  'DurableObjectName',
  'methodName',
  () => durableObjectStub.method(),
  { userId, platform }
);
```

### 4. Log Appropriately

```typescript
// The middleware automatically logs errors with appropriate detail levels:
// - Development: Full stack traces and technical details
// - Production: Structured logs without sensitive information
```

## Testing Error Handling

```typescript
import { ValidationError, RpcError } from '../common/error';

// Test error throwing
expect(() => {
  if (!userId) throw ValidationError.missingField('userId');
}).toThrow(ValidationError);

// Test error transformation
const transformedError = RpcErrorTransformer.transform(
  new Error('User is already connected to facebook'),
  { platform: 'facebook' }
);
expect(transformedError).toBeInstanceOf(ConflictError);
```

## Performance Considerations

1. **Error Transformation**: Minimal overhead, only processes errors when they occur
2. **Localization**: Messages are resolved on-demand, not pre-loaded
3. **Logging**: Structured logging reduces serialization overhead
4. **Middleware**: Single middleware handles all error types efficiently

## Migration Guide

To migrate existing error handling:

1. Replace manual error responses with error classes
2. Add error middleware to your Hono app
3. Wrap RPC calls with `handleRpcCall`
4. Update error codes in localization files
5. Test error scenarios thoroughly

This system provides comprehensive, consistent, and maintainable error handling across your entire Cloudflare Workers API.
