import { ErrorCode } from '../web/localizationMessages';
import { ApiErrorDetails, ErrorCategory, HttpStatusCode } from './types';

/**
 * Base class for all API errors with structured error handling
 */
export abstract class BaseApiError extends Error {
  public readonly category: ErrorCategory;
  public readonly statusCode: HttpStatusCode;
  public readonly errorCode: ErrorCode;
  public readonly technicalDetails?: any;
  public readonly context?: Record<string, any>;
  public readonly originalError?: Error;
  public readonly timestamp: string;

  constructor(message: string, details: ApiErrorDetails) {
    super(message);
    this.name = this.constructor.name;
    this.category = details.category;
    this.statusCode = details.statusCode;
    this.errorCode = details.errorCode;
    this.technicalDetails = details.technicalDetails;
    this.context = details.context;
    this.originalError = details.originalError;
    this.timestamp = new Date().toISOString();

    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * Convert error to JSON for API responses
   */
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      category: this.category,
      statusCode: this.statusCode,
      errorCode: this.errorCode,
      timestamp: this.timestamp,
      context: this.context,
      ...(this.technicalDetails && { technicalDetails: this.technicalDetails }),
    };
  }

  /**
   * Check if this error should be retried
   */
  isRetryable(): boolean {
    return [
      HttpStatusCode.TOO_MANY_REQUESTS,
      HttpStatusCode.BAD_GATEWAY,
      HttpStatusCode.SERVICE_UNAVAILABLE,
      HttpStatusCode.GATEWAY_TIMEOUT,
    ].includes(this.statusCode);
  }
}

/**
 * Validation errors (400 Bad Request)
 */
export class ValidationError extends BaseApiError {
  constructor(
    message: string,
    errorCode: ErrorCode = 'VALIDATION_FAILED',
    technicalDetails?: any,
    context?: Record<string, any>,
  ) {
    super(message, {
      category: ErrorCategory.VALIDATION,
      statusCode: HttpStatusCode.BAD_REQUEST,
      errorCode,
      technicalDetails,
      context,
    });
  }

  static missingField(fieldName: string): ValidationError {
    return new ValidationError(
      `Missing required field: ${fieldName}`,
      'MISSING_REQUIRED_FIELD',
      { fieldName },
      { field: fieldName },
    );
  }

  static invalidFormat(fieldName: string, expectedFormat: string): ValidationError {
    return new ValidationError(
      `Invalid format for field: ${fieldName}`,
      'INVALID_FIELD_FORMAT',
      { fieldName, expectedFormat },
      { field: fieldName, format: expectedFormat },
    );
  }

  static invalidValue(fieldName: string, value: any, allowedValues?: any[]): ValidationError {
    return new ValidationError(
      `Invalid value for field: ${fieldName}`,
      'INVALID_FIELD_VALUE',
      { fieldName, value, allowedValues },
      { field: fieldName, value, allowedValues },
    );
  }
}

/**
 * Authentication errors (401 Unauthorized)
 */
export class AuthenticationError extends BaseApiError {
  constructor(
    message: string,
    errorCode: ErrorCode = 'UNAUTHENTICATED',
    technicalDetails?: any,
    context?: Record<string, any>,
  ) {
    super(message, {
      category: ErrorCategory.AUTHENTICATION,
      statusCode: HttpStatusCode.UNAUTHORIZED,
      errorCode,
      technicalDetails,
      context,
    });
  }

  static invalidToken(): AuthenticationError {
    return new AuthenticationError('Invalid or expired authentication token', 'INVALID_AUTH_TOKEN');
  }

  static missingToken(): AuthenticationError {
    return new AuthenticationError('Authentication token is required', 'MISSING_AUTH_TOKEN');
  }

  static sessionExpired(): AuthenticationError {
    return new AuthenticationError('Session has expired', 'SESSION_EXPIRED');
  }
}

/**
 * Authorization errors (403 Forbidden)
 */
export class AuthorizationError extends BaseApiError {
  constructor(
    message: string,
    errorCode: ErrorCode = 'FORBIDDEN',
    technicalDetails?: any,
    context?: Record<string, any>,
  ) {
    super(message, {
      category: ErrorCategory.AUTHORIZATION,
      statusCode: HttpStatusCode.FORBIDDEN,
      errorCode,
      technicalDetails,
      context,
    });
  }

  static insufficientPermissions(resource: string): AuthorizationError {
    return new AuthorizationError(
      `Insufficient permissions to access: ${resource}`,
      'INSUFFICIENT_PERMISSIONS',
      { resource },
      { resource },
    );
  }

  static resourceForbidden(resource: string): AuthorizationError {
    return new AuthorizationError(
      `Access to resource is forbidden: ${resource}`,
      'RESOURCE_FORBIDDEN',
      { resource },
      { resource },
    );
  }
}

/**
 * Not found errors (404 Not Found)
 */
export class NotFoundError extends BaseApiError {
  constructor(
    message: string,
    errorCode: ErrorCode = 'RESOURCE_NOT_FOUND',
    technicalDetails?: any,
    context?: Record<string, any>,
  ) {
    super(message, {
      category: ErrorCategory.NOT_FOUND,
      statusCode: HttpStatusCode.NOT_FOUND,
      errorCode,
      technicalDetails,
      context,
    });
  }

  static resource(resourceType: string, identifier: string): NotFoundError {
    return new NotFoundError(
      `${resourceType} not found: ${identifier}`,
      'RESOURCE_NOT_FOUND',
      { resourceType, identifier },
      { resourceType, identifier },
    );
  }

  static link(linkId: string): NotFoundError {
    return new NotFoundError(`Link not found: ${linkId}`, 'LINK_NOT_FOUND', { linkId }, { linkId });
  }

  static user(userId: string): NotFoundError {
    return new NotFoundError(`User not found: ${userId}`, 'USER_NOT_FOUND', { userId }, { userId });
  }
}

/**
 * Conflict errors (409 Conflict)
 */
export class ConflictError extends BaseApiError {
  constructor(
    message: string,
    errorCode: ErrorCode = 'RESOURCE_CONFLICT',
    technicalDetails?: any,
    context?: Record<string, any>,
  ) {
    super(message, {
      category: ErrorCategory.CONFLICT,
      statusCode: HttpStatusCode.CONFLICT,
      errorCode,
      technicalDetails,
      context,
    });
  }

  static alreadyExists(resourceType: string, identifier: string): ConflictError {
    return new ConflictError(
      `${resourceType} already exists: ${identifier}`,
      'RESOURCE_ALREADY_EXISTS',
      { resourceType, identifier },
      { resourceType, identifier },
    );
  }

  static alreadyConnected(platform: string): ConflictError {
    return new ConflictError(
      `User is already connected to ${platform}`,
      'ALREADY_CONNECTED',
      { platform },
      { platform },
    );
  }
}

/**
 * Rate limit errors (429 Too Many Requests)
 */
export class RateLimitError extends BaseApiError {
  constructor(
    message: string,
    errorCode: ErrorCode = 'RATE_LIMIT_EXCEEDED',
    technicalDetails?: any,
    context?: Record<string, any>,
  ) {
    super(message, {
      category: ErrorCategory.RATE_LIMIT,
      statusCode: HttpStatusCode.TOO_MANY_REQUESTS,
      errorCode,
      technicalDetails,
      context,
    });
  }

  static retryLimitExceeded(platform: string, retryCount: number): RateLimitError {
    return new RateLimitError(
      `Retry limit exceeded for ${platform}`,
      'RETRY_LIMIT_EXCEEDED',
      { platform, retryCount },
      { platform, retryCount },
    );
  }
}

/**
 * RPC errors for Durable Object communication (502 Bad Gateway)
 */
export class RpcError extends BaseApiError {
  constructor(
    message: string,
    errorCode: ErrorCode = 'RPC_COMMUNICATION_FAILED',
    technicalDetails?: any,
    context?: Record<string, any>,
    originalError?: Error,
  ) {
    super(message, {
      category: ErrorCategory.RPC,
      statusCode: HttpStatusCode.BAD_GATEWAY,
      errorCode,
      technicalDetails,
      context,
      originalError,
    });
  }

  static communicationFailed(doName: string, method: string, originalError?: Error): RpcError {
    return new RpcError(
      `RPC communication failed with ${doName}.${method}`,
      'RPC_COMMUNICATION_FAILED',
      { doName, method, originalError: originalError?.message },
      { doName, method },
      originalError,
    );
  }

  static methodNotFound(doName: string, method: string): RpcError {
    return new RpcError(
      `RPC method not found: ${doName}.${method}`,
      'RPC_METHOD_NOT_FOUND',
      { doName, method },
      { doName, method },
    );
  }

  static timeout(doName: string, method: string, timeoutMs: number): RpcError {
    return new RpcError(
      `RPC timeout after ${timeoutMs}ms: ${doName}.${method}`,
      'RPC_TIMEOUT',
      { doName, method, timeoutMs },
      { doName, method, timeout: timeoutMs },
    );
  }

  static invalidResponse(doName: string, method: string, response: any): RpcError {
    return new RpcError(
      `Invalid RPC response from ${doName}.${method}`,
      'RPC_INVALID_RESPONSE',
      { doName, method, response },
      { doName, method },
    );
  }
}

/**
 * Internal server errors (500 Internal Server Error)
 */
export class InternalServerError extends BaseApiError {
  constructor(
    message: string,
    errorCode: ErrorCode = 'INTERNAL_SERVER_ERROR',
    technicalDetails?: any,
    context?: Record<string, any>,
    originalError?: Error,
  ) {
    super(message, {
      category: ErrorCategory.INTERNAL,
      statusCode: HttpStatusCode.INTERNAL_SERVER_ERROR,
      errorCode,
      technicalDetails,
      context,
      originalError,
    });
  }

  static generic(originalError?: Error): InternalServerError {
    return new InternalServerError(
      'An internal server error occurred',
      'INTERNAL_SERVER_ERROR',
      { originalError: originalError?.message, stack: originalError?.stack },
      {},
      originalError,
    );
  }

  static databaseError(operation: string, originalError?: Error): InternalServerError {
    return new InternalServerError(
      `Database operation failed: ${operation}`,
      'DATABASE_ERROR',
      { operation, originalError: originalError?.message },
      { operation },
      originalError,
    );
  }

  static configurationError(setting: string): InternalServerError {
    return new InternalServerError(
      `Configuration error: ${setting}`,
      'CONFIGURATION_ERROR',
      { setting },
      { setting },
    );
  }
}

/**
 * External service errors (502 Bad Gateway)
 */
export class ExternalServiceError extends BaseApiError {
  constructor(
    message: string,
    errorCode: ErrorCode = 'EXTERNAL_SERVICE_ERROR',
    technicalDetails?: any,
    context?: Record<string, any>,
    originalError?: Error,
  ) {
    super(message, {
      category: ErrorCategory.EXTERNAL,
      statusCode: HttpStatusCode.BAD_GATEWAY,
      errorCode,
      technicalDetails,
      context,
      originalError,
    });
  }

  static serviceUnavailable(serviceName: string): ExternalServiceError {
    return new ExternalServiceError(
      `External service unavailable: ${serviceName}`,
      'EXTERNAL_SERVICE_UNAVAILABLE',
      { serviceName },
      { serviceName },
    );
  }

  static authenticationFailed(serviceName: string): ExternalServiceError {
    return new ExternalServiceError(
      `Authentication failed with external service: ${serviceName}`,
      'EXTERNAL_SERVICE_AUTH_FAILED',
      { serviceName },
      { serviceName },
    );
  }
}

/**
 * Network errors (503 Service Unavailable)
 */
export class NetworkError extends BaseApiError {
  constructor(
    message: string,
    errorCode: ErrorCode = 'NETWORK_ERROR',
    technicalDetails?: any,
    context?: Record<string, any>,
    originalError?: Error,
  ) {
    super(message, {
      category: ErrorCategory.NETWORK,
      statusCode: HttpStatusCode.SERVICE_UNAVAILABLE,
      errorCode,
      technicalDetails,
      context,
      originalError,
    });
  }

  static connectionFailed(endpoint: string, originalError?: Error): NetworkError {
    return new NetworkError(
      `Network connection failed: ${endpoint}`,
      'NETWORK_CONNECTION_FAILED',
      { endpoint, originalError: originalError?.message },
      { endpoint },
      originalError,
    );
  }

  static timeout(endpoint: string, timeoutMs: number): NetworkError {
    return new NetworkError(
      `Network timeout after ${timeoutMs}ms: ${endpoint}`,
      'NETWORK_TIMEOUT',
      { endpoint, timeoutMs },
      { endpoint, timeout: timeoutMs },
    );
  }
}
