import { ErrorCode } from '../web/localizationMessages';

export type LogLevel = 'error' | 'warn' | 'info' | 'debug';

export type ErrorSource =
  | 'browser_connection'
  | 'script_injection'
  | 'captcha_detection'
  | 'screen_cropper'
  | 'workflow'
  | 'agent'
  | 'htmx'
  | 'network'
  | 'cdp'
  | 'api'
  | 'rpc'
  | 'validation'
  | 'authentication'
  | 'authorization';

export interface ErrorContext {
  source: ErrorSource;
  logLevel: LogLevel;
  errorCode: ErrorCode;
  technicalDetails: any;
  timestamp: string;
  userId: string;
  platformId: string;
  sessionId?: string;
  step?: string;
  referenceId: string;
  additionalContext?: Record<string, any>;
}

export interface ProcessedError {
  userMessage: string;
  shouldReplaceCard: boolean;
  errorCode: ErrorCode;
  logLevel: LogLevel;
}

/**
 * HTTP Status Code mappings for different error types
 */
export enum HttpStatusCode {
  OK = 200,
  CREATED = 201,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  TOO_MANY_REQUESTS = 429,
  INTERNAL_SERVER_ERROR = 500,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
  GATEWAY_TIMEOUT = 504,
}

/**
 * Error categories for classification
 */
export enum ErrorCategory {
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  NOT_FOUND = 'not_found',
  CONFLICT = 'conflict',
  RATE_LIMIT = 'rate_limit',
  RPC = 'rpc',
  INTERNAL = 'internal',
  EXTERNAL = 'external',
  NETWORK = 'network',
}

/**
 * Base interface for all API errors
 */
export interface ApiErrorDetails {
  /** Error category for classification */
  category: ErrorCategory;
  /** HTTP status code */
  statusCode: HttpStatusCode;
  /** Error code for localization */
  errorCode: ErrorCode;
  /** Technical details for logging */
  technicalDetails?: any;
  /** Additional context */
  context?: Record<string, any>;
  /** Original error if this is a wrapped error */
  originalError?: Error;
}

/**
 * Options for error display in UI
 */
export interface ErrorDisplayOptions {
  /** Whether to show technical details */
  showTechnicalDetails?: boolean;
  /** Whether to show retry button */
  showRetryButton?: boolean;
  /** Custom retry action */
  retryAction?: () => void;
}

export interface ErrorDisplayOptions {
  replaceCard?: boolean;
  dismissible?: boolean;
  showRetry?: boolean;
  autoHide?: boolean;
}
