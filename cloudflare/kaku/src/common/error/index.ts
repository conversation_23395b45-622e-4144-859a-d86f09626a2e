// Error handling system exports
export { ErrorService, ErrorHandlers } from './ErrorService';
export { ErrorCollector } from './ErrorCollector';
export { ErrorRouter } from './ErrorRouter';
export type {
  ErrorContext,
  ProcessedError,
  ErrorSource,
  LogLevel,
  ErrorDisplayOptions,
  ApiErrorDetails,
  ErrorCategory,
  HttpStatusCode,
} from './types';

// API Error Classes
export {
  BaseApiError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  RpcError,
  InternalServerError,
  ExternalServiceError,
  NetworkError,
} from './ApiErrors';

// RPC Error Transformation
export { RpcErrorTransformer } from './RpcErrorTransformer';

// API Error Middleware
export {
  createApiErrorMiddleware,
  rpcErrorMiddleware,
  developmentErrorMiddleware,
  productionErrorMiddleware,
  getErrorMiddleware,
  handleRpcCall,
  createApiErrorResponse,
} from './ApiErrorMiddleware';
export type { ApiErrorMiddlewareConfig, ApiErrorResponse } from './ApiErrorMiddleware';

// UI Components
export {
  ErrorDisplay,
  CriticalError,
  WarningBanner,
  InfoNotification,
} from '../../ui/components/error-display';
