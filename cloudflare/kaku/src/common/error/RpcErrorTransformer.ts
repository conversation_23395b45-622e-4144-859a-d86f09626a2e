import {
  BaseApiError,
  Rpc<PERSON>rror,
  InternalServerError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
} from './ApiErrors';

/**
 * Error patterns for RPC error classification
 */
interface ErrorPattern {
  pattern: RegExp;
  errorFactory: (match: RegExpMatchArray, originalError: Error) => BaseApiError;
}

/**
 * RPC Error Transformer - Converts generic RPC errors into typed API errors
 */
export class RpcErrorTransformer {
  private static readonly ERROR_PATTERNS: ErrorPattern[] = [
    // Authentication errors
    {
      pattern: /unauthorized|unauthenticated|invalid.*token|authentication.*failed/i,
      errorFactory: (match, originalError) =>
        AuthenticationError.invalidToken(),
    },
    {
      pattern: /session.*expired|token.*expired/i,
      errorFactory: (match, originalError) =>
        AuthenticationError.sessionExpired(),
    },

    // Authorization errors
    {
      pattern: /forbidden|access.*denied|insufficient.*permission/i,
      errorFactory: (match, originalError) =>
        AuthorizationError.insufficientPermissions('resource'),
    },

    // Not found errors
    {
      pattern: /not.*found|does.*not.*exist/i,
      errorFactory: (match, originalError) => {
        const message = originalError.message;
        if (message.includes('link')) {
          const linkMatch = message.match(/link.*?([a-zA-Z0-9-]+)/i);
          return NotFoundError.link(linkMatch?.[1] || 'unknown');
        }
        if (message.includes('user')) {
          const userMatch = message.match(/user.*?([a-zA-Z0-9-]+)/i);
          return NotFoundError.user(userMatch?.[1] || 'unknown');
        }
        return new NotFoundError(originalError.message, 'RESOURCE_NOT_FOUND');
      },
    },

    // Conflict errors
    {
      pattern: /already.*exists|already.*connected|conflict/i,
      errorFactory: (match, originalError) => {
        const message = originalError.message;
        if (message.includes('connected')) {
          const platformMatch = message.match(/connected.*?to.*?(\w+)/i);
          return ConflictError.alreadyConnected(platformMatch?.[1] || 'platform');
        }
        return new ConflictError(originalError.message, 'RESOURCE_CONFLICT');
      },
    },

    // Rate limit errors
    {
      pattern: /retry.*limit.*exceeded|too.*many.*requests|rate.*limit/i,
      errorFactory: (match, originalError) => {
        const message = originalError.message;
        const platformMatch = message.match(/for.*?(\w+)/i);
        const retryMatch = message.match(/(\d+)/);
        return RateLimitError.retryLimitExceeded(
          platformMatch?.[1] || 'platform',
          retryMatch ? parseInt(retryMatch[1]) : 0
        );
      },
    },

    // Validation errors
    {
      pattern: /invalid.*format|missing.*required|validation.*failed/i,
      errorFactory: (match, originalError) =>
        new ValidationError(originalError.message, 'VALIDATION_FAILED'),
    },

    // RPC-specific errors
    {
      pattern: /method.*not.*found|rpc.*method/i,
      errorFactory: (match, originalError) => {
        const methodMatch = originalError.message.match(/method.*?(\w+)/i);
        return RpcError.methodNotFound('DurableObject', methodMatch?.[1] || 'unknown');
      },
    },
    {
      pattern: /timeout|timed.*out/i,
      errorFactory: (match, originalError) => {
        const timeoutMatch = originalError.message.match(/(\d+).*?ms/i);
        return RpcError.timeout(
          'DurableObject',
          'unknown',
          timeoutMatch ? parseInt(timeoutMatch[1]) : 5000
        );
      },
    },
    {
      pattern: /connection.*failed|communication.*failed/i,
      errorFactory: (match, originalError) =>
        RpcError.communicationFailed('DurableObject', 'unknown', originalError),
    },
  ];

  /**
   * Transform a generic error into a typed API error
   */
  static transform(
    error: Error,
    context: {
      doName?: string;
      method?: string;
      userId?: string;
      linkId?: string;
      platform?: string;
    } = {}
  ): BaseApiError {
    // If it's already a BaseApiError, return as-is
    if (error instanceof BaseApiError) {
      return error;
    }

    const errorMessage = error.message || error.toString();

    // Try to match against known patterns
    for (const { pattern, errorFactory } of this.ERROR_PATTERNS) {
      const match = errorMessage.match(pattern);
      if (match) {
        try {
          const transformedError = errorFactory(match, error);
          // Enhance with context if available
          return this.enhanceWithContext(transformedError, context, error);
        } catch (factoryError) {
          console.warn('Error factory failed:', factoryError);
          // Continue to next pattern
        }
      }
    }

    // If no pattern matches, create a generic RPC error
    return this.createGenericRpcError(error, context);
  }

  /**
   * Transform RPC errors specifically for Durable Object calls
   */
  static transformRpcCall<T>(
    doName: string,
    method: string,
    rpcCall: () => Promise<T>,
    context: {
      userId?: string;
      linkId?: string;
      platform?: string;
    } = {}
  ): Promise<T> {
    return rpcCall().catch((error) => {
      const transformedError = this.transform(error, {
        doName,
        method,
        ...context,
      });
      throw transformedError;
    });
  }

  /**
   * Enhance error with additional context
   */
  private static enhanceWithContext(
    error: BaseApiError,
    context: {
      doName?: string;
      method?: string;
      userId?: string;
      linkId?: string;
      platform?: string;
    },
    originalError: Error
  ): BaseApiError {
    // Create a new error with enhanced context
    const enhancedContext = {
      ...error.context,
      ...context,
      originalErrorName: originalError.name,
      originalErrorStack: originalError.stack,
    };

    const enhancedTechnicalDetails = {
      ...error.technicalDetails,
      rpcContext: context,
      originalError: {
        message: originalError.message,
        name: originalError.name,
        stack: originalError.stack,
      },
    };

    // Return a new instance with enhanced details
    return new (error.constructor as any)(
      error.message,
      error.errorCode,
      enhancedTechnicalDetails,
      enhancedContext
    );
  }

  /**
   * Create a generic RPC error when no pattern matches
   */
  private static createGenericRpcError(
    error: Error,
    context: {
      doName?: string;
      method?: string;
      userId?: string;
      linkId?: string;
      platform?: string;
    }
  ): BaseApiError {
    const doName = context.doName || 'DurableObject';
    const method = context.method || 'unknown';

    // Check if it looks like a network/connection issue
    if (this.isNetworkError(error)) {
      return RpcError.communicationFailed(doName, method, error);
    }

    // Check if it looks like a timeout
    if (this.isTimeoutError(error)) {
      return RpcError.timeout(doName, method, 5000);
    }

    // Check if it looks like an invalid response
    if (this.isInvalidResponseError(error)) {
      return RpcError.invalidResponse(doName, method, error.message);
    }

    // Default to internal server error for unclassified errors
    return InternalServerError.generic(error);
  }

  /**
   * Check if error is network-related
   */
  private static isNetworkError(error: Error): boolean {
    const networkKeywords = [
      'network',
      'connection',
      'fetch',
      'socket',
      'dns',
      'host',
      'unreachable',
    ];
    const message = error.message.toLowerCase();
    return networkKeywords.some((keyword) => message.includes(keyword));
  }

  /**
   * Check if error is timeout-related
   */
  private static isTimeoutError(error: Error): boolean {
    const timeoutKeywords = ['timeout', 'timed out', 'deadline', 'aborted'];
    const message = error.message.toLowerCase();
    return timeoutKeywords.some((keyword) => message.includes(keyword));
  }

  /**
   * Check if error is invalid response-related
   */
  private static isInvalidResponseError(error: Error): boolean {
    const responseKeywords = [
      'invalid response',
      'malformed',
      'parse error',
      'json',
      'syntax error',
    ];
    const message = error.message.toLowerCase();
    return responseKeywords.some((keyword) => message.includes(keyword));
  }

  /**
   * Utility method to wrap any async function with RPC error transformation
   */
  static wrapAsync<T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    context: {
      doName?: string;
      method?: string;
      userId?: string;
      linkId?: string;
      platform?: string;
    } = {}
  ): (...args: T) => Promise<R> {
    return async (...args: T): Promise<R> => {
      try {
        return await fn(...args);
      } catch (error) {
        throw this.transform(error as Error, context);
      }
    };
  }
}
