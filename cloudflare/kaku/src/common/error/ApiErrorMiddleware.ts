import { Context, Next, MiddlewareHandler } from 'hono';
import { HTTPException } from 'hono/http-exception';
import { BaseApiError } from './ApiErrors';
import { RpcErrorTransformer } from './RpcErrorTransformer';
import { createErrorDetail, formatErrorResponse } from '../web/httpHelpers';
import { SupportedLocales } from '../web/localizationMessages';
import { KakuApp } from '../types';
import { HttpStatusCode } from './types';

/**
 * Configuration for API error middleware
 */
export interface ApiErrorMiddlewareConfig {
  /** Whether to include technical details in error responses (default: false in production) */
  includeTechnicalDetails?: boolean;
  /** Default locale for error messages */
  defaultLocale?: SupportedLocales;
  /** Custom error logger function */
  logger?: (error: BaseApiError, context: Context) => void;
  /** Whether to transform RPC errors automatically */
  transformRpcErrors?: boolean;
}

/**
 * API Error Response format
 */
export interface ApiErrorResponse {
  error: {
    code: string;
    message: string;
    category: string;
    timestamp: string;
    path: string;
    method: string;
    statusCode: number;
    details?: any;
    context?: Record<string, any>;
  };
  requestId?: string;
}

/**
 * Extract locale from request headers
 */
function extractLocale(request: Request): SupportedLocales {
  const acceptLanguage = request.headers.get('Accept-Language');
  if (!acceptLanguage) return 'en';

  // Parse Accept-Language header and find best match
  const languages = acceptLanguage
    .split(',')
    .map(lang => lang.split(';')[0].trim().toLowerCase());

  for (const lang of languages) {
    if (lang.startsWith('pt')) return 'pt';
    if (lang.startsWith('es')) return 'es';
    if (lang.startsWith('en')) return 'en';
  }

  return 'en';
}

/**
 * Default error logger
 */
function defaultLogger(error: BaseApiError, context: Context): void {
  const logLevel = error.statusCode >= 500 ? 'error' : 'warn';
  const logData = {
    error: error.name,
    message: error.message,
    category: error.category,
    statusCode: error.statusCode,
    path: context.req.path,
    method: context.req.method,
    userId: context.get('userId'),
    linkId: context.get('linkId'),
    timestamp: error.timestamp,
    ...(error.technicalDetails && { technicalDetails: error.technicalDetails }),
  };

  if (logLevel === 'error') {
    console.error('[API Error]', logData);
  } else {
    console.warn('[API Warning]', logData);
  }
}

/**
 * Create API error middleware for Hono
 */
export function createApiErrorMiddleware(
  config: ApiErrorMiddlewareConfig = {}
): MiddlewareHandler<KakuApp> {
  const {
    includeTechnicalDetails = false,
    defaultLocale = 'en',
    logger = defaultLogger,
    transformRpcErrors = true,
  } = config;

  return async (c: Context<KakuApp>, next: Next) => {
    try {
      await next();
    } catch (error) {
      // Transform the error into a BaseApiError
      let apiError: BaseApiError;

      if (error instanceof BaseApiError) {
        apiError = error;
      } else if (error instanceof HTTPException) {
        // Handle Hono's HTTPException
        apiError = RpcErrorTransformer.transform(
          new Error(error.message),
          {
            userId: c.get('userId'),
            linkId: c.get('linkId'),
            platform: c.get('serviceId'),
          }
        );
      } else if (transformRpcErrors && error instanceof Error) {
        // Transform generic errors using RPC transformer
        apiError = RpcErrorTransformer.transform(error, {
          userId: c.get('userId'),
          linkId: c.get('linkId'),
          platform: c.get('serviceId'),
        });
      } else {
        // Fallback for unknown error types
        apiError = RpcErrorTransformer.transform(
          new Error(String(error)),
          {
            userId: c.get('userId'),
            linkId: c.get('linkId'),
            platform: c.get('serviceId'),
          }
        );
      }

      // Log the error
      logger(apiError, c);

      // Extract locale from request
      const locale = extractLocale(c.req.raw) || defaultLocale;

      // Create error response
      const errorResponse = createApiErrorResponse(
        apiError,
        c.req.path,
        c.req.method,
        locale,
        includeTechnicalDetails
      );

      // Return error response
      return c.json(errorResponse, apiError.statusCode);
    }
  };
}

/**
 * Create standardized API error response
 */
export function createApiErrorResponse(
  error: BaseApiError,
  path: string,
  method: string,
  locale: SupportedLocales = 'en',
  includeTechnicalDetails: boolean = false
): ApiErrorResponse {
  const errorDetail = createErrorDetail(error.errorCode, locale);

  const response: ApiErrorResponse = {
    error: {
      code: error.errorCode,
      message: errorDetail.message,
      category: error.category,
      timestamp: error.timestamp,
      path,
      method,
      statusCode: error.statusCode,
    },
  };

  // Add context if available
  if (error.context && Object.keys(error.context).length > 0) {
    response.error.context = error.context;
  }

  // Add technical details if requested and available
  if (includeTechnicalDetails && error.technicalDetails) {
    response.error.details = error.technicalDetails;
  }

  return response;
}

/**
 * Utility function to handle RPC calls with automatic error transformation
 */
export async function handleRpcCall<T>(
  doName: string,
  method: string,
  rpcCall: () => Promise<T>,
  context: {
    userId?: string;
    linkId?: string;
    platform?: string;
  } = {}
): Promise<T> {
  return RpcErrorTransformer.transformRpcCall(doName, method, rpcCall, context);
}

/**
 * Middleware specifically for RPC error handling
 */
export function rpcErrorMiddleware(): MiddlewareHandler<KakuApp> {
  return createApiErrorMiddleware({
    transformRpcErrors: true,
    includeTechnicalDetails: false,
  });
}

/**
 * Development middleware with detailed error information
 */
export function developmentErrorMiddleware(): MiddlewareHandler<KakuApp> {
  
  return createApiErrorMiddleware({
    transformRpcErrors: true,
    includeTechnicalDetails: true,
    logger: (error, context) => {
      // Enhanced logging for development
      console.error('[DEV API Error]', {
        error: error.name,
        message: error.message,
        category: error.category,
        statusCode: error.statusCode,
        path: context.req.path,
        method: context.req.method,
        stack: error.stack,
        originalError: error.originalError,
        technicalDetails: error.technicalDetails,
        context: error.context,
        timestamp: error.timestamp,
      });
    },
  });
}

/**
 * Production middleware with minimal error information
 */
export function productionErrorMiddleware(): MiddlewareHandler<KakuApp> {
  return createApiErrorMiddleware({
    transformRpcErrors: true,
    includeTechnicalDetails: false,
    logger: (error, context) => {
      // Structured logging for production
      const logData = {
        level: error.statusCode >= 500 ? 'error' : 'warn',
        error: error.name,
        category: error.category,
        statusCode: error.statusCode,
        path: context.req.path,
        method: context.req.method,
        userId: context.get('userId'),
        linkId: context.get('linkId'),
        timestamp: error.timestamp,
        errorCode: error.errorCode,
      };

      // Only include technical details for 5xx errors
      if (error.statusCode >= 500 && error.technicalDetails) {
        logData['technicalDetails'] = error.technicalDetails;
      }

      console.log(JSON.stringify(logData));
    },
  });
}

/**
 * Utility to determine which error middleware to use based on environment
 */
export function getErrorMiddleware(environment: string = 'production'): MiddlewareHandler<KakuApp> {
  switch (environment.toLowerCase()) {
    case 'local':
    case 'development':
    case 'dev':
      return developmentErrorMiddleware();
    case 'production':
    case 'prod':
    default:
      return productionErrorMiddleware();
  }
}
