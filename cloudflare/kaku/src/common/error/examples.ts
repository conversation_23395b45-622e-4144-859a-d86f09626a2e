/**
 * Practical Examples of Error Handling System Usage
 * 
 * This file demonstrates real-world usage patterns for the comprehensive
 * error handling system in various scenarios.
 */

import { Context } from 'hono';
import { KakuApp } from '../types';
import {
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  RpcError,
  InternalServerError,
  handleRpcCall,
  RpcErrorTransformer,
} from './index';

// ============================================================================
// 1. API ENDPOINT EXAMPLES
// ============================================================================

/**
 * Example: User Registration Endpoint
 */
export async function registerUserExample(c: Context<KakuApp>) {
  const body = await c.req.json();
  const { email, password, platform } = body;

  // Input validation with specific error types
  if (!email) {
    throw ValidationError.missingField('email');
  }
  
  if (!email.includes('@')) {
    throw ValidationError.invalidFormat('email', 'valid email address');
  }
  
  if (!password || password.length < 8) {
    throw ValidationError.invalidValue('password', 'too short', ['minimum 8 characters']);
  }
  
  if (!['facebook', 'google', 'github'].includes(platform)) {
    throw ValidationError.invalidValue('platform', platform, ['facebook', 'google', 'github']);
  }

  // Check if user already exists
  const existingUser = await getUserByEmail(email);
  if (existingUser) {
    throw ConflictError.alreadyExists('User', email);
  }

  // Create user with RPC error handling
  const coordinator = c.env.CoordinatorDO.idFromName(email);
  const stub = c.env.CoordinatorDO.get(coordinator);
  
  const result = await handleRpcCall(
    'CoordinatorDO',
    'createUser',
    () => stub.createUser({ email, password, platform }),
    { userId: email, platform }
  );

  return c.json({ success: true, userId: result.userId });
}

/**
 * Example: Link Creation with Comprehensive Error Handling
 */
export async function createLinkExample(c: Context<KakuApp>) {
  const body = await c.req.json();
  const { serviceId, userId } = body;

  // Validation
  if (!serviceId) throw ValidationError.missingField('serviceId');
  if (!userId) throw ValidationError.missingField('userId');

  // Authorization check
  const user = c.get('user');
  if (!user) {
    throw AuthenticationError.missingToken();
  }

  if (user.id !== userId) {
    throw AuthorizationError.insufficientPermissions('create link for other users');
  }

  // Rate limiting check
  const userLinks = await getUserLinkCount(userId);
  if (userLinks >= 10) {
    throw RateLimitError.retryLimitExceeded('link creation', userLinks);
  }

  // Platform validation
  const validPlatforms = ['facebook', 'google', 'github'];
  if (!validPlatforms.includes(serviceId)) {
    throw ValidationError.invalidValue('serviceId', serviceId, validPlatforms);
  }

  // Check if already connected
  const existingConnection = await checkExistingConnection(userId, serviceId);
  if (existingConnection) {
    throw ConflictError.alreadyConnected(serviceId);
  }

  // Create link with RPC error handling
  const coordinator = c.env.CoordinatorDO.idFromName(userId);
  const stub = c.env.CoordinatorDO.get(coordinator);
  
  const linkResponse = await handleRpcCall(
    'CoordinatorDO',
    'createLink',
    () => stub.createLink(serviceId, userId),
    { userId, platform: serviceId }
  );

  return c.json(linkResponse);
}

// ============================================================================
// 2. RPC ERROR TRANSFORMATION EXAMPLES
// ============================================================================

/**
 * Example: Manual RPC Error Transformation
 */
export async function manualRpcErrorExample(c: Context<KakuApp>) {
  const { linkId } = c.req.param();
  const userId = c.get('userId');

  try {
    const coordinator = c.env.CoordinatorDO.idFromName(userId);
    const stub = c.env.CoordinatorDO.get(coordinator);
    const status = await stub.getStatus(linkId);
    
    if (!status) {
      throw NotFoundError.link(linkId);
    }
    
    return c.json(status);
  } catch (error) {
    // Transform any unexpected RPC errors
    const transformedError = RpcErrorTransformer.transform(error as Error, {
      doName: 'CoordinatorDO',
      method: 'getStatus',
      userId,
      linkId,
    });
    throw transformedError;
  }
}

/**
 * Example: Batch RPC Operations with Error Handling
 */
export async function batchOperationExample(c: Context<KakuApp>) {
  const { linkIds } = await c.req.json();
  const userId = c.get('userId');

  if (!Array.isArray(linkIds) || linkIds.length === 0) {
    throw ValidationError.invalidValue('linkIds', linkIds, ['non-empty array']);
  }

  if (linkIds.length > 50) {
    throw ValidationError.invalidValue('linkIds', `${linkIds.length} items`, ['maximum 50 items']);
  }

  const coordinator = c.env.CoordinatorDO.idFromName(userId);
  const stub = c.env.CoordinatorDO.get(coordinator);

  const results = [];
  const errors = [];

  for (const linkId of linkIds) {
    try {
      const status = await handleRpcCall(
        'CoordinatorDO',
        'getStatus',
        () => stub.getStatus(linkId),
        { userId, linkId }
      );
      results.push({ linkId, status });
    } catch (error) {
      errors.push({ linkId, error: error.message });
    }
  }

  return c.json({ results, errors });
}

// ============================================================================
// 3. AUTHENTICATION & AUTHORIZATION EXAMPLES
// ============================================================================

/**
 * Example: JWT Token Validation
 */
export async function validateTokenExample(token: string) {
  if (!token) {
    throw AuthenticationError.missingToken();
  }

  try {
    const decoded = await verifyJWT(token);
    if (decoded.exp < Date.now() / 1000) {
      throw AuthenticationError.sessionExpired();
    }
    return decoded;
  } catch (error) {
    throw AuthenticationError.invalidToken();
  }
}

/**
 * Example: Role-Based Authorization
 */
export async function checkPermissionExample(user: any, resource: string, action: string) {
  if (!user) {
    throw AuthenticationError.missingToken();
  }

  const requiredRole = getRequiredRole(resource, action);
  if (!user.roles.includes(requiredRole)) {
    throw AuthorizationError.insufficientPermissions(`${action} on ${resource}`);
  }

  // Check resource-specific permissions
  if (resource === 'link' && !user.ownedLinks.includes(action)) {
    throw AuthorizationError.resourceForbidden(resource);
  }
}

// ============================================================================
// 4. EXTERNAL SERVICE INTEGRATION EXAMPLES
// ============================================================================

/**
 * Example: External API Call with Error Handling
 */
export async function externalApiExample(endpoint: string, data: any) {
  try {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw AuthenticationError.invalidToken();
      }
      if (response.status === 403) {
        throw AuthorizationError.insufficientPermissions('external service');
      }
      if (response.status === 404) {
        throw NotFoundError.resource('external resource', endpoint);
      }
      if (response.status >= 500) {
        throw InternalServerError.generic(new Error(`External service error: ${response.status}`));
      }
    }

    return await response.json();
  } catch (error) {
    if (error instanceof BaseApiError) {
      throw error;
    }
    
    // Transform network errors
    throw RpcErrorTransformer.transform(error as Error, {
      doName: 'ExternalService',
      method: 'apiCall',
    });
  }
}

// ============================================================================
// 5. UTILITY FUNCTIONS
// ============================================================================

// Mock functions for examples (replace with actual implementations)
async function getUserByEmail(email: string): Promise<any> {
  // Implementation would check database/storage
  return null;
}

async function getUserLinkCount(userId: string): Promise<number> {
  // Implementation would count user's links
  return 0;
}

async function checkExistingConnection(userId: string, platform: string): Promise<boolean> {
  // Implementation would check if user is already connected to platform
  return false;
}

async function verifyJWT(token: string): Promise<any> {
  // Implementation would verify JWT token
  return { exp: Date.now() / 1000 + 3600 };
}

function getRequiredRole(resource: string, action: string): string {
  // Implementation would determine required role
  return 'user';
}

// ============================================================================
// 6. ERROR HANDLING MIDDLEWARE EXAMPLES
// ============================================================================

/**
 * Example: Custom Error Middleware with Metrics
 */
export function createMetricsErrorMiddleware() {
  return async (c: Context<KakuApp>, next: any) => {
    const startTime = Date.now();
    
    try {
      await next();
    } catch (error) {
      const duration = Date.now() - startTime;
      
      // Record error metrics
      recordErrorMetric({
        path: c.req.path,
        method: c.req.method,
        errorType: error.constructor.name,
        statusCode: error.statusCode || 500,
        duration,
        userId: c.get('userId'),
      });
      
      throw error; // Re-throw to be handled by main error middleware
    }
  };
}

function recordErrorMetric(data: any) {
  // Implementation would send metrics to monitoring service
  console.log('Error metric:', data);
}

/**
 * Example: Request ID Middleware for Error Tracking
 */
export function requestIdMiddleware() {
  return async (c: Context<KakuApp>, next: any) => {
    const requestId = generateRequestId();
    c.set('requestId', requestId);
    c.header('X-Request-ID', requestId);
    
    try {
      await next();
    } catch (error) {
      // Add request ID to error context
      if (error.context) {
        error.context.requestId = requestId;
      }
      throw error;
    }
  };
}

function generateRequestId(): string {
  return Math.random().toString(36).substring(2, 15);
}
