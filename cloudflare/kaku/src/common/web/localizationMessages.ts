export const localizationMessages = {
  en: {
    UNAUTHENTICATED: 'Unauthenticated',
    // Critical System Errors
    BROWSER_CONNECTION_FAILED: 'Unable to establish browser connection',
    SCRIPT_INJECTION_FAILED: 'System initialization failed',
    WORKFLOW_STEP_FAILED: 'Process encountered an issue',
    CDP_SESSION_FAILED: 'Browser session could not be established',
    // Client Script Errors
    SCREEN_CROPPER_INIT_FAILED: 'Screen capture initialization failed',
    CAPTCHA_DETECTOR_INIT_FAILED: 'Captcha detection initialization failed',
    BROWSER_CONTROLLER_INIT_FAILED: 'Browser controller initialization failed',
    // Network/Communication Errors
    WEBSOCKET_CONNECTION_FAILED: 'Connection to service lost',
    AGENT_COMMUNICATION_FAILED: 'Service communication error',
    // Generic Messages
    SYSTEM_ERROR: 'A system error occurred',
    TEMPORARY_ISSUE: 'We encountered a temporary issue',
    TRY_AGAIN_LATER: 'Please try again in a few moments',

    // API Validation Errors
    VALIDATION_FAILED: 'The provided data is invalid',
    MISSING_REQUIRED_FIELD: 'Required field is missing: {field}',
    INVALID_FIELD_FORMAT: 'Invalid format for field: {field}',
    INVALID_FIELD_VALUE: 'Invalid value for field: {field}',

    // Authentication Errors
    INVALID_AUTH_TOKEN: 'Your session has expired. Please log in again',
    MISSING_AUTH_TOKEN: 'Authentication is required to access this resource',
    SESSION_EXPIRED: 'Your session has expired. Please log in again',

    // Authorization Errors
    FORBIDDEN: 'You do not have permission to access this resource',
    INSUFFICIENT_PERMISSIONS: 'You do not have sufficient permissions for this action',
    RESOURCE_FORBIDDEN: 'Access to this resource is not allowed',

    // Resource Errors
    RESOURCE_NOT_FOUND: 'The requested resource was not found',
    LINK_NOT_FOUND: 'The requested link was not found or has expired',
    USER_NOT_FOUND: 'User account not found',

    // Conflict Errors
    RESOURCE_CONFLICT: 'The resource already exists or conflicts with existing data',
    RESOURCE_ALREADY_EXISTS: 'This resource already exists',
    ALREADY_CONNECTED: 'You are already connected to this platform',

    // Rate Limiting
    RATE_LIMIT_EXCEEDED: 'Too many requests. Please try again later',
    RETRY_LIMIT_EXCEEDED: 'Maximum retry attempts exceeded. Please try again later',

    // RPC Errors
    RPC_COMMUNICATION_FAILED: 'Service communication error. Please try again',
    RPC_METHOD_NOT_FOUND: 'The requested operation is not available',
    RPC_TIMEOUT: 'The operation timed out. Please try again',
    RPC_INVALID_RESPONSE: 'Invalid response from service. Please try again',

    // Internal Errors
    INTERNAL_SERVER_ERROR: 'An internal error occurred. Please try again later',
    DATABASE_ERROR: 'Database operation failed. Please try again',
    CONFIGURATION_ERROR: 'System configuration error. Please contact support',

    // External Service Errors
    EXTERNAL_SERVICE_ERROR: 'External service error. Please try again later',
    EXTERNAL_SERVICE_UNAVAILABLE: 'External service is currently unavailable',
    EXTERNAL_SERVICE_AUTH_FAILED: 'Authentication with external service failed',

    // Network Errors
    NETWORK_ERROR: 'Network connection error. Please check your connection',
    NETWORK_CONNECTION_FAILED: 'Failed to connect to service. Please try again',
    NETWORK_TIMEOUT: 'Network request timed out. Please try again',
  },
  pt: {
    UNAUTHENTICATED: 'Não autenticado',
    BROWSER_CONNECTION_FAILED: 'Não foi possível estabelecer conexão com o navegador',
    SCRIPT_INJECTION_FAILED: 'Falha na inicialização do sistema',
    WORKFLOW_STEP_FAILED: 'Processo encontrou um problema',
    CDP_SESSION_FAILED: 'Sessão do navegador não pôde ser estabelecida',
    SCREEN_CROPPER_INIT_FAILED: 'Falha na inicialização da captura de tela',
    CAPTCHA_DETECTOR_INIT_FAILED: 'Falha na inicialização da detecção de captcha',
    BROWSER_CONTROLLER_INIT_FAILED: 'Falha na inicialização do controlador do navegador',
    WEBSOCKET_CONNECTION_FAILED: 'Conexão com o serviço perdida',
    AGENT_COMMUNICATION_FAILED: 'Erro de comunicação do serviço',
    SYSTEM_ERROR: 'Ocorreu um erro do sistema',
    TEMPORARY_ISSUE: 'Encontramos um problema temporário',
    TRY_AGAIN_LATER: 'Por favor, tente novamente em alguns momentos',

    // API Validation Errors
    VALIDATION_FAILED: 'Os dados fornecidos são inválidos',
    MISSING_REQUIRED_FIELD: 'Campo obrigatório está faltando: {field}',
    INVALID_FIELD_FORMAT: 'Formato inválido para o campo: {field}',
    INVALID_FIELD_VALUE: 'Valor inválido para o campo: {field}',

    // Authentication Errors
    INVALID_AUTH_TOKEN: 'Sua sessão expirou. Por favor, faça login novamente',
    MISSING_AUTH_TOKEN: 'Autenticação é necessária para acessar este recurso',
    SESSION_EXPIRED: 'Sua sessão expirou. Por favor, faça login novamente',

    // Authorization Errors
    FORBIDDEN: 'Você não tem permissão para acessar este recurso',
    INSUFFICIENT_PERMISSIONS: 'Você não tem permissões suficientes para esta ação',
    RESOURCE_FORBIDDEN: 'Acesso a este recurso não é permitido',

    // Resource Errors
    RESOURCE_NOT_FOUND: 'O recurso solicitado não foi encontrado',
    LINK_NOT_FOUND: 'O link solicitado não foi encontrado ou expirou',
    USER_NOT_FOUND: 'Conta de usuário não encontrada',

    // Conflict Errors
    RESOURCE_CONFLICT: 'O recurso já existe ou conflita com dados existentes',
    RESOURCE_ALREADY_EXISTS: 'Este recurso já existe',
    ALREADY_CONNECTED: 'Você já está conectado a esta plataforma',

    // Rate Limiting
    RATE_LIMIT_EXCEEDED: 'Muitas solicitações. Tente novamente mais tarde',
    RETRY_LIMIT_EXCEEDED: 'Máximo de tentativas excedido. Tente novamente mais tarde',

    // RPC Errors
    RPC_COMMUNICATION_FAILED: 'Erro de comunicação do serviço. Tente novamente',
    RPC_METHOD_NOT_FOUND: 'A operação solicitada não está disponível',
    RPC_TIMEOUT: 'A operação expirou. Tente novamente',
    RPC_INVALID_RESPONSE: 'Resposta inválida do serviço. Tente novamente',

    // Internal Errors
    INTERNAL_SERVER_ERROR: 'Ocorreu um erro interno. Tente novamente mais tarde',
    DATABASE_ERROR: 'Operação do banco de dados falhou. Tente novamente',
    CONFIGURATION_ERROR: 'Erro de configuração do sistema. Entre em contato com o suporte',

    // External Service Errors
    EXTERNAL_SERVICE_ERROR: 'Erro de serviço externo. Tente novamente mais tarde',
    EXTERNAL_SERVICE_UNAVAILABLE: 'Serviço externo está indisponível no momento',
    EXTERNAL_SERVICE_AUTH_FAILED: 'Falha na autenticação com serviço externo',

    // Network Errors
    NETWORK_ERROR: 'Erro de conexão de rede. Verifique sua conexão',
    NETWORK_CONNECTION_FAILED: 'Falha ao conectar ao serviço. Tente novamente',
    NETWORK_TIMEOUT: 'Solicitação de rede expirou. Tente novamente',
  },
  es: {
    UNAUTHENTICATED: 'No autenticado',
    BROWSER_CONNECTION_FAILED: 'No se pudo establecer conexión con el navegador',
    SCRIPT_INJECTION_FAILED: 'Falló la inicialización del sistema',
    WORKFLOW_STEP_FAILED: 'El proceso encontró un problema',
    CDP_SESSION_FAILED: 'No se pudo establecer la sesión del navegador',
    SCREEN_CROPPER_INIT_FAILED: 'Falló la inicialización de captura de pantalla',
    CAPTCHA_DETECTOR_INIT_FAILED: 'Falló la inicialización de detección de captcha',
    BROWSER_CONTROLLER_INIT_FAILED: 'Falló la inicialización del controlador del navegador',
    WEBSOCKET_CONNECTION_FAILED: 'Se perdió la conexión con el servicio',
    AGENT_COMMUNICATION_FAILED: 'Error de comunicación del servicio',
    SYSTEM_ERROR: 'Ocurrió un error del sistema',
    TEMPORARY_ISSUE: 'Encontramos un problema temporal',
    TRY_AGAIN_LATER: 'Por favor, inténtalo de nuevo en unos momentos',

    // API Validation Errors
    VALIDATION_FAILED: 'Los datos proporcionados son inválidos',
    MISSING_REQUIRED_FIELD: 'Falta el campo requerido: {field}',
    INVALID_FIELD_FORMAT: 'Formato inválido para el campo: {field}',
    INVALID_FIELD_VALUE: 'Valor inválido para el campo: {field}',

    // Authentication Errors
    INVALID_AUTH_TOKEN: 'Tu sesión ha expirado. Por favor, inicia sesión nuevamente',
    MISSING_AUTH_TOKEN: 'Se requiere autenticación para acceder a este recurso',
    SESSION_EXPIRED: 'Tu sesión ha expirado. Por favor, inicia sesión nuevamente',

    // Authorization Errors
    FORBIDDEN: 'No tienes permiso para acceder a este recurso',
    INSUFFICIENT_PERMISSIONS: 'No tienes permisos suficientes para esta acción',
    RESOURCE_FORBIDDEN: 'El acceso a este recurso no está permitido',

    // Resource Errors
    RESOURCE_NOT_FOUND: 'El recurso solicitado no fue encontrado',
    LINK_NOT_FOUND: 'El enlace solicitado no fue encontrado o ha expirado',
    USER_NOT_FOUND: 'Cuenta de usuario no encontrada',

    // Conflict Errors
    RESOURCE_CONFLICT: 'El recurso ya existe o entra en conflicto con datos existentes',
    RESOURCE_ALREADY_EXISTS: 'Este recurso ya existe',
    ALREADY_CONNECTED: 'Ya estás conectado a esta plataforma',

    // Rate Limiting
    RATE_LIMIT_EXCEEDED: 'Demasiadas solicitudes. Inténtalo de nuevo más tarde',
    RETRY_LIMIT_EXCEEDED: 'Máximo de intentos excedido. Inténtalo de nuevo más tarde',

    // RPC Errors
    RPC_COMMUNICATION_FAILED: 'Error de comunicación del servicio. Inténtalo de nuevo',
    RPC_METHOD_NOT_FOUND: 'La operación solicitada no está disponible',
    RPC_TIMEOUT: 'La operación expiró. Inténtalo de nuevo',
    RPC_INVALID_RESPONSE: 'Respuesta inválida del servicio. Inténtalo de nuevo',

    // Internal Errors
    INTERNAL_SERVER_ERROR: 'Ocurrió un error interno. Inténtalo de nuevo más tarde',
    DATABASE_ERROR: 'La operación de base de datos falló. Inténtalo de nuevo',
    CONFIGURATION_ERROR: 'Error de configuración del sistema. Contacta al soporte',

    // External Service Errors
    EXTERNAL_SERVICE_ERROR: 'Error de servicio externo. Inténtalo de nuevo más tarde',
    EXTERNAL_SERVICE_UNAVAILABLE: 'El servicio externo no está disponible actualmente',
    EXTERNAL_SERVICE_AUTH_FAILED: 'Falló la autenticación con el servicio externo',

    // Network Errors
    NETWORK_ERROR: 'Error de conexión de red. Verifica tu conexión',
    NETWORK_CONNECTION_FAILED: 'Falló la conexión al servicio. Inténtalo de nuevo',
    NETWORK_TIMEOUT: 'La solicitud de red expiró. Inténtalo de nuevo',
  },
};

export type SupportedLocales = keyof typeof localizationMessages;
export type ErrorCode = keyof (typeof localizationMessages)['en'];
